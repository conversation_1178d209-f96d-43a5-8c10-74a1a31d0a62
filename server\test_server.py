#!/usr/bin/env python3
"""
WCM Server Test Script
WCM服务器测试脚本
"""
import requests
import json
import time
import sys
from typing import Dict, Any

class WCMServerTester:
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.token = None
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 健康检查通过: {data.get('message', '')}")
                return True
            else:
                print(f"✗ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 健康检查异常: {e}")
            return False
    
    def test_login(self, username: str = "admin", password: str = "password") -> bool:
        """测试登录"""
        try:
            login_data = {
                "username": username,
                "password": password
            }
            response = self.session.post(
                f"{self.base_url}/api/auth/login",
                json=login_data
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                print(f"✓ 登录成功")
                return True
            else:
                print(f"✗ 登录失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 登录异常: {e}")
            return False
    
    def test_get_user_info(self) -> bool:
        """测试获取用户信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/auth/me")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 用户信息获取成功: {data.get('username', '')}")
                return True
            else:
                print(f"✗ 用户信息获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 用户信息获取异常: {e}")
            return False
    
    def test_system_info(self) -> bool:
        """测试系统信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/system/info")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 系统信息获取成功")
                return True
            else:
                print(f"✗ 系统信息获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 系统信息获取异常: {e}")
            return False
    
    def test_vms_list(self) -> bool:
        """测试虚拟机列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/vms")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 虚拟机列表获取成功")
                return True
            else:
                print(f"✗ 虚拟机列表获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 虚拟机列表获取异常: {e}")
            return False
    
    def test_networks_list(self) -> bool:
        """测试网络列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/networks")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 网络列表获取成功")
                return True
            else:
                print(f"✗ 网络列表获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 网络列表获取异常: {e}")
            return False
    
    def test_topology_templates(self) -> bool:
        """测试拓扑模板"""
        try:
            response = self.session.get(f"{self.base_url}/api/topology/templates")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 拓扑模板获取成功: {len(data)} 个模板")
                return True
            else:
                print(f"✗ 拓扑模板获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 拓扑模板获取异常: {e}")
            return False
    
    def test_common_issues(self) -> bool:
        """测试常见问题"""
        try:
            response = self.session.get(f"{self.base_url}/api/troubleshoot/common-issues")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 常见问题获取成功: {len(data)} 个问题")
                return True
            else:
                print(f"✗ 常见问题获取失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 常见问题获取异常: {e}")
            return False
    
    def test_deploy_vm(self) -> bool:
        """测试虚拟机部署"""
        try:
            deploy_data = {
                "vm_name": "test_vm",
                "config": {
                    "memory": "2048",
                    "cpu": "2"
                }
            }
            response = self.session.post(
                f"{self.base_url}/api/deploy",
                json=deploy_data
            )
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 虚拟机部署测试成功: {data.get('message', '')}")
                return True
            else:
                print(f"✗ 虚拟机部署测试失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 虚拟机部署测试异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("=" * 50)
        print("WCM Server API 测试")
        print("=" * 50)
        
        results = {}
        
        # 基础测试
        print("\n1. 基础功能测试")
        results["health_check"] = self.test_health_check()
        results["login"] = self.test_login()
        
        if results["login"]:
            # 需要认证的测试
            print("\n2. 认证功能测试")
            results["user_info"] = self.test_get_user_info()
            results["system_info"] = self.test_system_info()
            
            print("\n3. 数据获取测试")
            results["vms_list"] = self.test_vms_list()
            results["networks_list"] = self.test_networks_list()
            results["topology_templates"] = self.test_topology_templates()
            results["common_issues"] = self.test_common_issues()
            
            print("\n4. 功能测试")
            results["deploy_vm"] = self.test_deploy_vm()
        
        # 统计结果
        print("\n" + "=" * 50)
        print("测试结果统计")
        print("=" * 50)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️  部分测试失败，请检查服务器状态")
        
        return results

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://127.0.0.1:8080"
    
    print(f"测试服务器: {base_url}")
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    tester = WCMServerTester(base_url)
    results = tester.run_all_tests()
    
    # 返回适当的退出码
    if all(results.values()):
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
