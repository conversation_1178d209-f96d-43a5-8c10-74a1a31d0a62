<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 登录</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .login-container {
            background-color: #ffffff; /* 登录框背景 */
            border: 1px solid #ddd; /* 柔和边框 */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 柔和阴影 */
            padding: 40px;
            border-radius: 8px;
            text-align: center;
            position: relative;
            z-index: 1;
            animation: none; /* 移除动画 */
        }
 
        h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin-bottom: 30px;
            font-size: 2.2em;
            letter-spacing: 1px;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-size: 0.9em;
            font-weight: 600;
        }

        input[type="text"],
        input[type="password"] {
            width: calc(100% - 22px); /* 调整宽度以适应边框 */
            padding: 10px;
            border: 1px solid #ccc;
            background-color: #fff;
            color: #333;
            border-radius: 4px;
            outline: none;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        input[type="text"]:focus,
        input[type="password"]:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>WCM</h1>
        <form>
            <div class="input-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" placeholder="输入您的用户名" required>
            </div>
            <div class="input-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" placeholder="输入您的密码" required>
            </div>
            <button type="submit">登录</button>
        </form>
    </div>
    <script>
        document.querySelector('form').addEventListener('submit', function(event) {
            event.preventDefault(); // 阻止表单默认提交

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // 模拟登录验证
            if (username === 'admin' && password === 'password') { // 简单模拟，实际应更复杂
                alert('登录成功！正在进入仪表板...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html'; // 模拟跳转
                }, 1500); // 1.5秒后跳转
            } else {
                alert('用户名或密码错误，请重试。');
            }
        });
    </script>
</body>
</html>