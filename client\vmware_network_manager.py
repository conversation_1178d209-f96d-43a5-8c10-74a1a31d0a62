"""
VMware LAN网络管理模块
"""
import os
import uuid
from typing import Dict, List, Optional, Any
from vmware_config_parser import VMwareConfigParser
from config import Config


class VMwareNetworkManager:
    """VMware LAN网络管理器"""
    
    def __init__(self):
        self.config_parser = VMwareConfigParser()
        
    def get_lan_networks(self) -> List[Dict[str, Any]]:
        """获取所有LAN网络"""
        preferences = self.config_parser.parse_preferences()
        return preferences["lan_networks"]
    
    def create_lan_network(self, network_name: str) -> Dict[str, Any]:
        """
        创建新的LAN网络
        """
        if not network_name or not network_name.strip():
            return {"success": False, "error": "网络名称不能为空"}
        
        network_name = network_name.strip()
        
        # 检查网络名称是否已存在
        existing_networks = self.get_lan_networks()
        for network in existing_networks:
            if network["name"] == network_name:
                return {"success": False, "error": f"网络名称 '{network_name}' 已存在"}
        
        try:
            # 生成新的PVN ID
            pvn_id = self._generate_pvn_id()
            
            # 读取当前preferences文件
            preferences_data = self.config_parser.parse_config_file(Config.PREFERENCES_FILE)
            
            # 获取当前网络数量
            current_count = int(preferences_data.get('pref.namedPVNs.count', '0'))
            new_index = current_count
            
            # 添加新网络配置
            preferences_data[f'pref.namedPVNs{new_index}.name'] = network_name
            preferences_data[f'pref.namedPVNs{new_index}.pvnID'] = pvn_id
            preferences_data['pref.namedPVNs.count'] = str(current_count + 1)
            
            # 写回文件
            self._write_preferences_file(preferences_data)
            
            return {
                "success": True,
                "network": {
                    "index": new_index,
                    "name": network_name,
                    "pvn_id": pvn_id
                }
            }
            
        except Exception as e:
            return {"success": False, "error": f"创建网络失败: {str(e)}"}
    
    def delete_lan_network(self, network_name: str) -> Dict[str, Any]:
        """
        删除LAN网络
        """
        if not network_name or not network_name.strip():
            return {"success": False, "error": "网络名称不能为空"}
        
        network_name = network_name.strip()
        
        try:
            # 读取当前preferences文件
            preferences_data = self.config_parser.parse_config_file(Config.PREFERENCES_FILE)
            
            # 查找要删除的网络
            current_count = int(preferences_data.get('pref.namedPVNs.count', '0'))
            target_index = None
            
            for i in range(current_count):
                if preferences_data.get(f'pref.namedPVNs{i}.name') == network_name:
                    target_index = i
                    break
            
            if target_index is None:
                return {"success": False, "error": f"网络 '{network_name}' 不存在"}
            
            # 删除网络配置
            del preferences_data[f'pref.namedPVNs{target_index}.name']
            del preferences_data[f'pref.namedPVNs{target_index}.pvnID']
            
            # 重新排列剩余网络的索引
            remaining_networks = []
            for i in range(current_count):
                if i != target_index:
                    name = preferences_data.get(f'pref.namedPVNs{i}.name')
                    pvn_id = preferences_data.get(f'pref.namedPVNs{i}.pvnID')
                    if name and pvn_id:
                        remaining_networks.append({"name": name, "pvn_id": pvn_id})
                    
                    # 删除旧的配置项
                    if f'pref.namedPVNs{i}.name' in preferences_data:
                        del preferences_data[f'pref.namedPVNs{i}.name']
                    if f'pref.namedPVNs{i}.pvnID' in preferences_data:
                        del preferences_data[f'pref.namedPVNs{i}.pvnID']
            
            # 重新写入网络配置
            for i, network in enumerate(remaining_networks):
                preferences_data[f'pref.namedPVNs{i}.name'] = network["name"]
                preferences_data[f'pref.namedPVNs{i}.pvnID'] = network["pvn_id"]
            
            # 更新网络数量
            preferences_data['pref.namedPVNs.count'] = str(len(remaining_networks))
            
            # 写回文件
            self._write_preferences_file(preferences_data)
            
            return {"success": True, "message": f"网络 '{network_name}' 已删除"}
            
        except Exception as e:
            return {"success": False, "error": f"删除网络失败: {str(e)}"}
    
    def rename_lan_network(self, old_name: str, new_name: str) -> Dict[str, Any]:
        """
        重命名LAN网络
        """
        if not old_name or not old_name.strip():
            return {"success": False, "error": "原网络名称不能为空"}
        
        if not new_name or not new_name.strip():
            return {"success": False, "error": "新网络名称不能为空"}
        
        old_name = old_name.strip()
        new_name = new_name.strip()
        
        if old_name == new_name:
            return {"success": False, "error": "新旧网络名称相同"}
        
        try:
            # 读取当前preferences文件
            preferences_data = self.config_parser.parse_config_file(Config.PREFERENCES_FILE)
            
            # 查找要重命名的网络
            current_count = int(preferences_data.get('pref.namedPVNs.count', '0'))
            target_index = None
            
            # 检查新名称是否已存在
            for i in range(current_count):
                network_name = preferences_data.get(f'pref.namedPVNs{i}.name')
                if network_name == new_name:
                    return {"success": False, "error": f"网络名称 '{new_name}' 已存在"}
                if network_name == old_name:
                    target_index = i
            
            if target_index is None:
                return {"success": False, "error": f"网络 '{old_name}' 不存在"}
            
            # 更新网络名称
            preferences_data[f'pref.namedPVNs{target_index}.name'] = new_name
            
            # 写回文件
            self._write_preferences_file(preferences_data)
            
            return {"success": True, "message": f"网络已从 '{old_name}' 重命名为 '{new_name}'"}
            
        except Exception as e:
            return {"success": False, "error": f"重命名网络失败: {str(e)}"}
    
    def _generate_pvn_id(self) -> str:
        """生成PVN ID"""
        # 生成类似VMware格式的ID: "xx xx xx xx xx xx xx xx-xx xx xx xx xx xx xx xx"
        random_uuid = uuid.uuid4()
        uuid_bytes = random_uuid.bytes
        
        # 转换为VMware格式的十六进制字符串
        hex_parts = []
        for i in range(0, 16, 2):
            hex_parts.append(f"{uuid_bytes[i]:02x} {uuid_bytes[i+1]:02x}")
        
        # 前8个字节和后8个字节用-分隔
        return f"{' '.join(hex_parts[:4])}-{' '.join(hex_parts[4:])}"
    
    def _write_preferences_file(self, preferences_data: Dict[str, str]) -> None:
        """写入preferences文件"""
        # 备份原文件
        backup_file = Config.PREFERENCES_FILE + ".backup"
        if os.path.exists(Config.PREFERENCES_FILE):
            import shutil
            shutil.copy2(Config.PREFERENCES_FILE, backup_file)
        
        # 写入新配置
        with open(Config.PREFERENCES_FILE, 'w', encoding='utf-8') as f:
            f.write('.encoding = "GBK"\n')
            
            # 按特定顺序写入配置项
            ordered_keys = self._get_ordered_preference_keys(preferences_data)
            
            for key in ordered_keys:
                if key in preferences_data and key != '.encoding':
                    value = preferences_data[key]
                    f.write(f'{key} = "{value}"\n')
    
    def _get_ordered_preference_keys(self, preferences_data: Dict[str, str]) -> List[str]:
        """获取有序的配置键列表"""
        # 定义配置项的优先级顺序
        priority_patterns = [
            'pref.keyboardAndMouse',
            'pref.ws.session',
            'pref.namedPVNs',
            'hints',
            'vmWizard',
            'hint',
            'pref.snapshotManager'
        ]
        
        ordered_keys = []
        remaining_keys = set(preferences_data.keys())
        
        # 按优先级模式排序
        for pattern in priority_patterns:
            pattern_keys = [key for key in remaining_keys if key.startswith(pattern)]
            pattern_keys.sort()
            ordered_keys.extend(pattern_keys)
            remaining_keys -= set(pattern_keys)
        
        # 添加剩余的键
        remaining_keys = sorted(remaining_keys)
        ordered_keys.extend(remaining_keys)
        
        return ordered_keys
