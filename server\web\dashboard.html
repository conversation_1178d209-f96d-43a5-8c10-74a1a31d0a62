<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 仪表板</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .user-info {
            color: #555;
            font-size: 0.9em;
        }

        .main-content {
            display: flex;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .sidebar {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            width: 250px;
            flex-shrink: 0;
            animation: none; /* 移除动画 */
        }

        .sidebar h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #333;
            text-shadow: none;
            margin-top: 0;
            margin-bottom: 25px;
            font-size: 1.4em;
            text-align: center;
        }

        .nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-list li {
            margin-bottom: 10px;
        }

        .nav-list a {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa; /* 浅色背景 */
            border: 1px solid #e9ecef; /* 柔和边框 */
            border-radius: 4px;
            color: #333;
            text-decoration: none;
            font-size: 1em;
            transition: background-color 0.2s, border-color 0.2s, box-shadow 0.2s, color 0.2s;
            box-shadow: none;
        }

        .nav-list a:hover {
            background-color: #e9ecef;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
            color: #007bff;
        }

        .nav-list a svg {
            margin-right: 10px;
            width: 20px;
            height: 20px;
            fill: currentColor; /* SVG 颜色与文本颜色一致 */
        }

        .content-area {
            flex: 1;
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 30px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5em;
            color: #555;
            text-shadow: none;
            animation: none; /* 移除动画 */
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="user-info">
            <span>欢迎, 管理员</span>
            <!-- 可以在这里添加用户设置或退出链接的SVG图标 -->
        </div>
    </div>

    <div class="main-content">
        <div class="sidebar">
            <h2>功能模块</h2>
            <ul class="nav-list">
                <li>
                    <a href="deploy.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7v10l10 5 10-5V7L12 2zm0 2.31L18.6 7l-6.6 3.69L5.4 7 12 4.31zM4 9v6l8 4 8-4V9l-8-4z" />
                        </svg>
                        一键部署
                    </a>
                </li>
                <li>
                    <a href="restore.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z" />
                        </svg>
                        环境恢复
                    </a>
                </li>
                <li>
                    <a href="topology.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 12c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM4 12c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm16 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM12 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM4 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm16 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
                        </svg>
                        拓扑选择与初始化
                    </a>
                </li>
                <li>
                    <a href="batch_deploy.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 9V7h-2V5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2H2v2h2v2H2v2h2v2H2v2c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-2h2v-2h-2v-2h2v-2h-2V9h2zm-4 10H6V5h12v14zM9 8h6v2H9zm0 4h6v2H9zm0 4h6v2H9z" />
                        </svg>
                        批量部署
                    </a>
                </li>
                <li>
                    <a href="scoring.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z" />
                        </svg>
                        自动化评分
                    </a>
                </li>
                <li>
                    <a href="troubleshooting.html">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
                        </svg>
                        故障排除模块
                    </a>
                </li>
            </ul>
        </div>
        <div class="content-area">
            选择一个功能开始操作
        </div>
    </div>
</body>
</html>