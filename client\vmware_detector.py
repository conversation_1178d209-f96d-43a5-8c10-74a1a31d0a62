"""
VMware Workstation 安装目录检测模块
"""
import os
import winreg
from pathlib import Path
from typing import Optional, List, Dict
import psutil
from config import Config


class VMwareDetector:
    """VMware Workstation 检测器"""
    
    def __init__(self):
        self.vmware_path = None
        self.version = None
        
    def detect_vmware_installation(self) -> Optional[str]:
        """
        检测VMware Workstation安装路径
        返回安装路径或None
        """
        # 方法1: 从注册表检测
        vmware_path = self._detect_from_registry()
        if vmware_path:
            self.vmware_path = vmware_path
            return vmware_path
            
        # 方法2: 从默认路径检测
        vmware_path = self._detect_from_default_paths()
        if vmware_path:
            self.vmware_path = vmware_path
            return vmware_path
            
        # 方法3: 从进程检测
        vmware_path = self._detect_from_processes()
        if vmware_path:
            self.vmware_path = vmware_path
            return vmware_path
            
        return None
    
    def _detect_from_registry(self) -> Optional[str]:
        """从Windows注册表检测VMware安装路径"""
        registry_paths = [
            r"SOFTWARE\VMware, Inc.\VMware Workstation",
            r"SOFTWARE\WOW6432Node\VMware, Inc.\VMware Workstation"
        ]
        
        for reg_path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                    install_path, _ = winreg.QueryValueEx(key, "InstallPath")
                    if os.path.exists(install_path):
                        # 获取版本信息
                        try:
                            version, _ = winreg.QueryValueEx(key, "ProductVersion")
                            self.version = version
                        except FileNotFoundError:
                            pass
                        return install_path
            except (FileNotFoundError, OSError):
                continue
        return None
    
    def _detect_from_default_paths(self) -> Optional[str]:
        """从默认安装路径检测"""
        for path in Config.VMWARE_DEFAULT_PATHS:
            if os.path.exists(path):
                vmware_exe = os.path.join(path, "vmware.exe")
                if os.path.exists(vmware_exe):
                    return path
        return None
    
    def _detect_from_processes(self) -> Optional[str]:
        """从运行的进程检测VMware路径"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'vmware' in proc.info['name'].lower():
                        if proc.info['exe']:
                            exe_path = Path(proc.info['exe'])
                            if exe_path.name.lower() == 'vmware.exe':
                                return str(exe_path.parent)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass
        return None
    
    def get_vmware_info(self) -> Dict[str, any]:
        """获取VMware安装信息"""
        install_path = self.detect_vmware_installation()
        
        info = {
            "installed": install_path is not None,
            "install_path": install_path,
            "version": self.version,
            "executable": None,
            "config_dir": Config.VMWARE_CONFIG_DIR,
            "config_exists": os.path.exists(Config.VMWARE_CONFIG_DIR)
        }
        
        if install_path:
            vmware_exe = os.path.join(install_path, "vmware.exe")
            info["executable"] = vmware_exe if os.path.exists(vmware_exe) else None
            
        return info
    
    def get_default_vm_directories(self) -> List[str]:
        """获取默认虚拟机目录"""
        directories = []
        for path in Config.DEFAULT_VM_PATHS:
            if os.path.exists(path):
                directories.append(path)
        return directories
    
    def is_vmware_running(self) -> bool:
        """检查VMware是否正在运行"""
        vmware_processes = ['vmware.exe', 'vmware-vmx.exe', 'vmnetdhcp.exe', 'vmnat.exe']
        
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and proc.info['name'].lower() in vmware_processes:
                    return True
        except Exception:
            pass
        return False
