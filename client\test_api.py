#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_health():
    """测试健康检查"""
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"Health check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_networks():
    """测试网络API"""
    try:
        response = requests.get(f"{BASE_URL}/api/networks")
        print(f"Networks: {response.status_code}")
        if response.status_code == 200:
            networks = response.json()
            print(f"Found {len(networks)} networks:")
            for net in networks:
                print(f"  - {net['name']} (index: {net['index']})")
        return response.status_code == 200
    except Exception as e:
        print(f"Networks test failed: {e}")
        return False

def test_vms():
    """测试虚拟机API"""
    try:
        response = requests.get(f"{BASE_URL}/api/vms")
        print(f"VMs: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            vms = data.get('virtual_machines', [])
            print(f"Found {len(vms)} virtual machines:")
            for vm in vms[:3]:  # 只显示前3个
                print(f"  - {vm['display_name']} (ID: {vm['id']}, Status: {'OK' if vm['exists'] else 'Missing'})")
        return response.status_code == 200
    except Exception as e:
        print(f"VMs test failed: {e}")
        return False

def test_vm_update():
    """测试虚拟机更新API"""
    try:
        # 测试更新虚拟机1的多网卡配置
        update_data = {
            "display_name": "测试虚拟机-多网卡",
            "network_adapters": [
                {
                    "index": 0,
                    "connection_type": "nat",
                    "address_type": "generated"
                },
                {
                    "index": 1,
                    "connection_type": "pvn",
                    "vnet": "LAN1",
                    "address_type": "generated"
                }
            ]
        }

        response = requests.put(
            f"{BASE_URL}/api/vms/1",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )

        print(f"VM Multi-NIC Update: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Update result: {result}")
        else:
            print(f"Update failed: {response.text}")

        return response.status_code == 200
    except Exception as e:
        print(f"VM update test failed: {e}")
        return False

def test_add_network_adapter():
    """测试添加网络适配器"""
    try:
        adapter_data = {
            "connection_type": "bridged",
            "address_type": "generated"
        }

        response = requests.post(
            f"{BASE_URL}/api/vms/2/network-adapters",
            json=adapter_data,
            headers={"Content-Type": "application/json"}
        )

        print(f"Add Network Adapter: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Add result: {result}")
        else:
            print(f"Add failed: {response.text}")

        return response.status_code == 200
    except Exception as e:
        print(f"Add network adapter test failed: {e}")
        return False

def test_remove_network_adapter():
    """测试删除网络适配器"""
    try:
        # 删除虚拟机2的网络适配器1
        response = requests.delete(f"{BASE_URL}/api/vms/2/network-adapters/1")

        print(f"Remove Network Adapter: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Remove result: {result}")
        else:
            print(f"Remove failed: {response.text}")

        return response.status_code == 200
    except Exception as e:
        print(f"Remove network adapter test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("=== VMware管理器API测试 ===\n")
    
    tests = [
        ("健康检查", test_health),
        ("网络列表", test_networks),
        ("虚拟机列表", test_vms),
        ("虚拟机多网卡更新", test_vm_update),
        ("添加网络适配器", test_add_network_adapter),
        ("删除网络适配器", test_remove_network_adapter),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"测试 {name}...")
        result = test_func()
        results.append((name, result))
        print(f"结果: {'✓ 通过' if result else '✗ 失败'}\n")
    
    print("=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✓" if result else "✗"
        print(f"{status} {name}")
    
    print(f"\n通过: {passed}/{total}")

if __name__ == "__main__":
    main()
