# WCM Server - VMware Configuration Manager

WCM (VMware Configuration Manager) 是一个基于 FastAPI 的 VMware Workstation 管理系统后端服务。

## 功能特性

### 🔐 用户认证
- JWT 令牌认证
- 基于角色的权限控制
- 安全的密码哈希

### 🖥️ 虚拟机管理
- 虚拟机列表查看
- 虚拟机配置管理
- 网络适配器管理

### 🚀 一键部署
- 单个虚拟机快速部署
- 模板化部署配置
- 部署状态监控

### 🔄 环境恢复
- 快照恢复
- 备份恢复
- 环境重置

### 🌐 网络拓扑管理
- 预定义拓扑模板
- 自定义网络配置
- 拓扑可视化支持

### 📦 批量部署
- 多虚拟机并行部署
- 批量配置管理
- 部署进度跟踪

### 📊 自动化评分
- 配置合规性检查
- 多维度评分标准
- 详细评分报告

### 🔧 故障排除
- 自动问题诊断
- 解决方案推荐
- 系统健康检查

## 安装和配置

### 1. 环境要求
- Python 3.8+
- VMware Workstation Pro/Player
- Windows 操作系统

### 2. 安装依赖
```bash
cd server
pip install -r requirements.txt
```

### 3. 配置环境变量（可选）
创建 `.env` 文件：
```env
WCM_HOST=127.0.0.1
WCM_PORT=8080
WCM_DEBUG=True
WCM_SECRET_KEY=your_secret_key_here
WCM_TOKEN_EXPIRE=30
WCM_LOG_LEVEL=INFO
```

### 4. 启动服务器
```bash
# 使用启动脚本（推荐）
python start_server.py

# 或直接启动
python main.py

# 或使用 uvicorn
uvicorn main:app --host 127.0.0.1 --port 8080 --reload
```

## API 文档

启动服务器后，访问以下地址查看 API 文档：
- Swagger UI: http://127.0.0.1:8080/docs
- ReDoc: http://127.0.0.1:8080/redoc

## 主要 API 端点

### 认证
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 系统信息
- `GET /api/system/info` - 获取系统信息
- `GET /api/health` - 健康检查

### 虚拟机管理
- `GET /api/vms` - 获取虚拟机列表
- `GET /api/vms/{vm_id}` - 获取虚拟机详情

### 网络管理
- `GET /api/networks` - 获取网络列表

### 功能模块
- `POST /api/deploy` - 一键部署
- `POST /api/restore` - 环境恢复
- `POST /api/topology/initialize` - 初始化拓扑
- `GET /api/topology/templates` - 获取拓扑模板
- `POST /api/batch-deploy` - 批量部署
- `POST /api/scoring/evaluate` - 自动化评分
- `POST /api/troubleshoot/diagnose` - 故障诊断
- `GET /api/troubleshoot/common-issues` - 常见问题

## Web 界面

服务器提供完整的 Web 界面：
- `/` - 登录页面
- `/dashboard` - 主仪表板
- `/deploy` - 一键部署
- `/restore` - 环境恢复
- `/topology` - 拓扑管理
- `/batch_deploy` - 批量部署
- `/scoring` - 自动化评分
- `/troubleshooting` - 故障排除

## 默认用户

- 用户名: `admin`
- 密码: `password`

**注意**: 生产环境中请修改默认密码！

## 配置说明

### 服务器配置
在 `config.py` 中可以修改：
- 服务器地址和端口
- JWT 密钥和过期时间
- 文件路径配置
- 日志配置

### VMware 配置
系统会自动检测 VMware Workstation 安装路径和配置文件。

### 拓扑模板
在配置文件中预定义了三种网络拓扑：
- 简单局域网 (5个节点)
- 路由网络 (8个节点)
- 企业网络 (15个节点)

## 开发说明

### 项目结构
```
server/
├── main.py              # 主应用文件
├── config.py            # 配置文件
├── start_server.py      # 启动脚本
├── requirements.txt     # 依赖列表
├── README.md           # 说明文档
└── web/                # Web 静态文件
    ├── index.html
    ├── dashboard.html
    └── ...
```

### 扩展功能
要添加新功能：
1. 在 `main.py` 中添加新的 API 端点
2. 创建对应的 Pydantic 模型
3. 实现业务逻辑
4. 更新前端页面（如需要）

## 故障排除

### 常见问题
1. **端口被占用**: 修改配置文件中的端口号
2. **VMware 未检测到**: 检查 VMware Workstation 是否正确安装
3. **权限问题**: 以管理员身份运行
4. **依赖缺失**: 运行 `pip install -r requirements.txt`

### 日志查看
日志文件位置: `wcm_server.log`

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题，请检查：
1. 系统要求是否满足
2. 依赖是否正确安装
3. VMware Workstation 是否正常运行
4. 日志文件中的错误信息
