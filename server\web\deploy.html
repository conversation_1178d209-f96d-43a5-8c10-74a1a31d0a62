<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 一键部署</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel label {
            font-size: 1.1em;
            color: #333;
        }

        .control-panel select {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            border-radius: 4px;
            outline: none;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .control-panel select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .deploy-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        .deploy-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .deploy-button:active {
            transform: translateY(0);
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 400px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <label for="environment-select">选择部署环境:</label>
            <select id="environment-select">
                <option value="nsm-infra-oam">网络系统管理 - 基础设施编程与自动化运维</option>
                <option value="worldskills-net">世界技能大赛 - 网络</option>
                <option value="national-net">全国网络大赛</option>
                <option value="provincial-net">省级网络大赛</option>
                <option value="municipal-net">市级网络大赛</option>
                <!-- 更多预定义拓扑 -->
            </select>
            <button class="deploy-button">一键部署</button>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待部署指令...
            <br>
            <br>
            [2025-07-11 23:40:00] 系统启动，准备就绪。
            <br>
            [2025-07-11 23:40:05] 检测到可用部署模板：网络系统管理 - 基础设施编程与自动化运维。
            <br>
            [2025-07-11 23:40:10] 请选择一个环境并点击“一键部署”按钮。
        </div>

        <!-- 移除 SVG 装饰 -->
        <!-- <svg class="svg-decoration svg-network" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
            <circle cx="150" cy="150" r="140" stroke-dasharray="5 5" />
            <line x1="50" y1="150" x2="250" y2="150" />
            <line x1="150" y1="50" x2="150" y2="250" />
            <circle cx="50" cy="150" r="10" fill="#00ffcc" />
            <circle cx="250" cy="150" r="10" fill="#00ffcc" />
            <circle cx="150" cy="50" r="10" fill="#00ffcc" />
            <circle cx="150" cy="250" r="10" fill="#00ffcc" />
            <circle cx="150" cy="150" r="20" fill="#ff00ff" />
            <text x="150" y="155" text-anchor="middle" fill="#0a0a0a" font-size="16" font-weight="bold">CORE</text>
        </svg> -->
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const deployButton = document.querySelector('.deploy-button');
            const terminalArea = document.querySelector('.terminal-area');
            const environmentSelect = document.getElementById('environment-select');

            const logMessages = [
                "正在初始化部署环境...",
                "正在下载虚拟机镜像: [VM_BASE_IMAGE_WIN10.vmdk]",
                "正在下载虚拟机镜像: [VM_BASE_IMAGE_LINUX.vmdk]",
                "正在配置网络参数和IP地址...",
                "正在创建虚拟机: VM-SRV-01 (Windows Server)",
                "正在创建虚拟机: VM-CLI-01 (Linux Client)",
                "正在启动虚拟机...",
                "正在执行自动化配置脚本...",
                "配置完成。正在进行连通性测试...",
                "连通性测试通过。部署成功！",
                "环境已准备就绪，可以开始竞赛或训练。"
            ];

            deployButton.addEventListener('click', function() {
                const selectedEnv = environmentSelect.options[environmentSelect.selectedIndex].text;
                terminalArea.innerHTML = `WCM >_ 正在部署环境: ${selectedEnv}<br><br>`;
                let i = 0;
                const interval = setInterval(() => {
                    if (i < logMessages.length) {
                        terminalArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${logMessages[i]}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight; // 滚动到底部
                        i++;
                    } else {
                        clearInterval(interval);
                        terminalArea.innerHTML += `<br>WCM >_ 部署过程已完成。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1000); // 每秒输出一条日志
            });
        });
    </script>
</body>
</html>