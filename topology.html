<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 拓扑选择与初始化</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
            width: 100%; /* 确保标题占据整行 */
        }

        .topology-selection {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
        }

        .topology-card {
            background-color: #f9f9f9; /* 浅色卡片背景 */
            border: 1px solid #ddd; /* 柔和边框 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            width: 180px;
        }

        .topology-card:hover {
            background-color: #f0f0f0;
            border-color: #007bff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-3px);
        }

        .topology-card.selected {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25); /* 选中状态的蓝色光晕 */
            background-color: #e6f2ff; /* 选中状态的浅蓝色背景 */
        }

        .topology-card svg {
            width: 100px;
            height: 100px;
            margin-bottom: 10px;
            fill: none;
            stroke: #007bff; /* SVG 描边颜色 */
            stroke-width: 1.5;
        }

        .topology-card h3 {
            margin: 0;
            font-size: 1.1em;
            color: #333;
        }

        .topology-details {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            animation: none; /* 移除动画 */
        }

        .topology-details h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff;
            text-shadow: none;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .topology-details p {
            margin-bottom: 10px;
            color: #555;
        }

        .topology-details svg {
            width: 100%;
            max-width: 400px;
            height: auto;
            margin-top: 15px;
            border: 1px dashed #ccc; /* 柔和虚线边框 */
            padding: 10px;
            box-sizing: border-box;
        }

        .initialize-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
            align-self: center; /* 按钮居中 */
        }

        .initialize-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .initialize-button:active {
            transform: translateY(0);
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 250px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <h2>选择并初始化拓扑</h2>
            <div class="topology-selection">
                <div class="topology-card selected" data-topology="nsm-infra-oam-basic">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="10" y="10" width="80" height="80" rx="5" ry="5" fill="none" stroke="#007bff" stroke-width="2"/>
                        <circle cx="30" cy="30" r="8" fill="#007bff"/>
                        <circle cx="70" cy="30" r="8" fill="#007bff"/>
                        <circle cx="30" cy="70" r="8" fill="#007bff"/>
                        <circle cx="70" cy="70" r="8" fill="#007bff"/>
                        <line x1="30" y1="30" x2="70" y2="30" stroke="#555" stroke-width="1"/>
                        <line x1="30" y1="30" x2="30" y2="70" stroke="#555" stroke-width="1"/>
                        <line x1="70" y1="30" x2="70" y2="70" stroke="#555" stroke-width="1"/>
                        <line x1="30" y1="70" x2="70" y2="70" stroke="#555" stroke-width="1"/>
                    </svg>
                    <h3>基础运维拓扑</h3>
                </div>
                <div class="topology-card" data-topology="worldskills-complex">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="20" r="8" fill="#007bff"/>
                        <circle cx="20" cy="50" r="8" fill="#007bff"/>
                        <circle cx="80" cy="50" r="8" fill="#007bff"/>
                        <circle cx="50" cy="80" r="8" fill="#007bff"/>
                        <line x1="50" y1="20" x2="20" y2="50" stroke="#555" stroke-width="1"/>
                        <line x1="50" y1="20" x2="80" y2="50" stroke="#555" stroke-width="1"/>
                        <line x1="20" y1="50" x2="50" y2="80" stroke="#555" stroke-width="1"/>
                        <line x1="80" y1="50" x2="50" y2="80" stroke="#555" stroke-width="1"/>
                        <line x1="20" y1="50" x2="80" y2="50" stroke="#007bff" stroke-width="1"/>
                    </svg>
                    <h3>世赛复杂拓扑</h3>
                </div>
                <div class="topology-card" data-topology="national-enterprise">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="10" y="40" width="20" height="20" fill="#007bff"/>
                        <rect x="70" y="40" width="20" height="20" fill="#007bff"/>
                        <circle cx="50" cy="50" r="10" fill="#007bff"/>
                        <line x1="30" y1="50" x2="50" y2="50" stroke="#555" stroke-width="2"/>
                        <line x1="50" y1="50" x2="70" y2="50" stroke="#555" stroke-width="2"/>
                        <text x="20" y="55" font-size="10" fill="#ffffff" text-anchor="middle">FW</text>
                        <text x="80" y="55" font-size="10" fill="#ffffff" text-anchor="middle">SRV</text>
                        <text x="50" y="45" font-size="10" fill="#ffffff" text-anchor="middle">RTR</text>
                    </svg>
                    <h3>全国企业拓扑</h3>
                </div>
            </div>
            <button class="initialize-button">初始化所选拓扑</button>
        </div>

        <div class="topology-details">
            <h2>拓扑详情: 基础运维拓扑</h2>
            <p>描述: 包含两台Linux服务器和一台Windows客户端的基础网络拓扑，用于基础设施编程与自动化运维模块的初步练习。</p>
            <p>虚拟机数量: 3</p>
            <p>网络段: 2</p>
            <p>预设配置: SSH, RDP, HTTP服务</p>
            <svg viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
                <rect x="50" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                <text x="100" y="85" text-anchor="middle" fill="#333" font-size="16">Linux-SRV1</text>
                <rect x="200" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                <text x="250" y="85" text-anchor="middle" fill="#333" font-size="16">Linux-SRV2</text>
                <rect x="350" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                <text x="400" y="85" text-anchor="middle" fill="#333" font-size="16">Win-CLI</text>

                <line x1="150" y1="80" x2="200" y2="80" stroke="#555" stroke-width="3" stroke-dasharray="5,5"/>
                <line x1="300" y1="80" x2="350" y2="80" stroke="#555" stroke-width="3" stroke-dasharray="5,5"/>

                <circle cx="175" cy="80" r="8" fill="#007bff"/>
                <circle cx="325" cy="80" r="8" fill="#007bff"/>

                <text x="175" y="105" text-anchor="middle" fill="#007bff" font-size="12">NET-A</text>
                <text x="325" y="105" text-anchor="middle" fill="#007bff" font-size="12">NET-B</text>

                <path d="M250 150 L220 180 L250 210 L280 180 Z" fill="#007bff" stroke="#007bff" stroke-width="1"/>
                <text x="250" y="185" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">RTR</text>

                <line x1="100" y1="110" x2="100" y2="150" stroke="#555" stroke-width="2"/>
                <line x1="250" y1="110" x2="250" y2="150" stroke="#555" stroke-width="2"/>
                <line x1="400" y1="110" x2="400" y2="150" stroke="#555" stroke-width="2"/>

                <line x1="250" y1="210" x2="250" y2="250" stroke="#555" stroke-width="2"/>
                <rect x="200" y="250" width="100" height="30" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                <text x="250" y="270" text-anchor="middle" fill="#333" font-size="14">Internet</text>
            </svg>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待拓扑初始化指令...
            <br>
            <br>
            [2025-07-11 23:50:00] 系统启动，准备就绪。
            <br>
            [2025-07-11 23:50:05] 检测到可用拓扑模板。
            <br>
            [2025-07-11 23:50:10] 请选择一个拓扑并点击“初始化所选拓扑”按钮。
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const topologyCards = document.querySelectorAll('.topology-card');
            const topologyDetails = document.querySelector('.topology-details');
            const initializeButton = document.querySelector('.initialize-button');
            const terminalArea = document.querySelector('.terminal-area');

            topologyCards.forEach(card => {
                card.addEventListener('click', function() {
                    topologyCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    updateTopologyDetails(this.dataset.topology);
                });
            });

            function updateTopologyDetails(topologyId) {
                let title = '';
                let description = '';
                let vmCount = '';
                let networkSegments = '';
                let presetConfigs = '';
                let svgContent = '';

                switch (topologyId) {
                    case 'nsm-infra-oam-basic':
                        title = '基础运维拓扑';
                        description = '包含两台Linux服务器和一台Windows客户端的基础网络拓扑，用于基础设施编程与自动化运维模块的初步练习。';
                        vmCount = '3';
                        networkSegments = '2';
                        presetConfigs = 'SSH, RDP, HTTP服务';
                        svgContent = `
                            <rect x="50" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="100" y="85" text-anchor="middle" fill="#333" font-size="16">Linux-SRV1</text>
                            <rect x="200" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="250" y="85" text-anchor="middle" fill="#333" font-size="16">Linux-SRV2</text>
                            <rect x="350" y="50" width="100" height="60" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="400" y="85" text-anchor="middle" fill="#333" font-size="16">Win-CLI</text>

                            <line x1="150" y1="80" x2="200" y2="80" stroke="#555" stroke-width="3" stroke-dasharray="5,5"/>
                            <line x1="300" y1="80" x2="350" y2="80" stroke="#555" stroke-width="3" stroke-dasharray="5,5"/>

                            <circle cx="175" cy="80" r="8" fill="#007bff"/>
                            <circle cx="325" cy="80" r="8" fill="#007bff"/>

                            <text x="175" y="105" text-anchor="middle" fill="#007bff" font-size="12">NET-A</text>
                            <text x="325" y="105" text-anchor="middle" fill="#007bff" font-size="12">NET-B</text>

                            <path d="M250 150 L220 180 L250 210 L280 180 Z" fill="#007bff" stroke="#007bff" stroke-width="1"/>
                            <text x="250" y="185" text-anchor="middle" fill="#ffffff" font-size="14" font-weight="bold">RTR</text>

                            <line x1="100" y1="110" x2="100" y2="150" stroke="#555" stroke-width="2"/>
                            <line x1="250" y1="110" x2="250" y2="150" stroke="#555" stroke-width="2"/>
                            <line x1="400" y1="110" x2="400" y2="150" stroke="#555" stroke-width="2"/>

                            <line x1="250" y1="210" x2="250" y2="250" stroke="#555" stroke-width="2"/>
                            <rect x="200" y="250" width="100" height="30" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="250" y="270" text-anchor="middle" fill="#333" font-size="14">Internet</text>
                        `;
                        break;
                    case 'worldskills-complex':
                        title = '世赛复杂拓扑';
                        description = '模拟世界技能大赛网络布线与管理项目的复杂拓扑，包含多层交换、路由、防火墙和多种操作系统虚拟机。';
                        vmCount = '10+';
                        networkSegments = '5+';
                        presetConfigs = 'VLAN, OSPF, ACL, VPN';
                        svgContent = `
                            <rect x="20" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="50" y="45" text-anchor="middle" fill="#333" font-size="14">RTR1</text>
                            <rect x="150" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="180" y="45" text-anchor="middle" fill="#333" font-size="14">SW1</text>
                            <rect x="280" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="310" y="45" text-anchor="middle" fill="#333" font-size="14">SRV1</text>

                            <line x1="80" y1="40" x2="150" y2="40" stroke="#555" stroke-width="2"/>
                            <line x1="210" y1="40" x2="280" y2="40" stroke="#555" stroke-width="2"/>

                            <rect x="20" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="50" y="125" text-anchor="middle" fill="#333" font-size="14">RTR2</text>
                            <rect x="150" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="180" y="125" text-anchor="middle" fill="#333" font-size="14">SW2</text>
                            <rect x="280" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="310" y="125" text-anchor="middle" fill="#333" font-size="14">CLI1</text>

                            <line x1="80" y1="120" x2="150" y2="120" stroke="#555" stroke-width="2"/>
                            <line x1="210" y1="120" x2="280" y2="120" stroke="#555" stroke-width="2"/>

                            <line x1="50" y1="60" x2="50" y2="100" stroke="#007bff" stroke-width="2" stroke-dasharray="3,3"/>
                            <line x1="180" y1="60" x2="180" y2="100" stroke="#007bff" stroke-width="2" stroke-dasharray="3,3"/>
                        `;
                        break;
                    case 'national-enterprise':
                        title = '全国企业拓扑';
                        description = '模拟中型企业网络环境，包含防火墙、多层交换机、服务器集群和多个部门的客户端，用于全国性竞赛。';
                        vmCount = '20+';
                        networkSegments = '10+';
                        presetConfigs = 'NAT, VPN, QoS, AD';
                        svgContent = `
                            <rect x="20" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="50" y="45" text-anchor="middle" fill="#333" font-size="14">FW</text>
                            <rect x="120" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="150" y="45" text-anchor="middle" fill="#333" font-size="14">RTR</text>
                            <rect x="220" y="20" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="250" y="45" text-anchor="middle" fill="#333" font-size="14">SW-C</text>

                            <line x1="80" y1="40" x2="120" y2="40" stroke="#555" stroke-width="2"/>
                            <line x1="180" y1="40" x2="220" y2="40" stroke="#555" stroke-width="2"/>

                            <rect x="20" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="50" y="125" text-anchor="middle" fill="#333" font-size="14">SRV-AD</text>
                            <rect x="120" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="150" y="125" text-anchor="middle" fill="#333" font-size="14">SRV-WEB</text>
                            <rect x="220" y="100" width="60" height="40" rx="5" ry="5" fill="#f0f0f0" stroke="#007bff" stroke-width="2"/>
                            <text x="250" y="125" text-anchor="middle" fill="#333" font-size="14">CLI-HR</text>

                            <line x1="50" y1="60" x2="50" y2="100" stroke="#007bff" stroke-width="2" stroke-dasharray="3,3"/>
                            <line x1="150" y1="60" x2="150" y2="100" stroke="#007bff" stroke-width="2" stroke-dasharray="3,3"/>
                            <line x1="250" y1="60" x2="250" y2="100" stroke="#007bff" stroke-width="2" stroke-dasharray="3,3"/>
                        `;
                        break;
                    default:
                        title = '未选择拓扑';
                        description = '请从上方选择一个预定义拓扑以查看其详细信息。';
                        vmCount = 'N/A';
                        networkSegments = 'N/A';
                        presetConfigs = 'N/A';
                        svgContent = '';
                }

                topologyDetails.querySelector('h2').textContent = `拓扑详情: ${title}`;
                topologyDetails.querySelector('p:nth-of-type(1)').textContent = `描述: ${description}`;
                topologyDetails.querySelector('p:nth-of-type(2)').textContent = `虚拟机数量: ${vmCount}`;
                topologyDetails.querySelector('p:nth-of-type(3)').textContent = `网络段: ${networkSegments}`;
                topologyDetails.querySelector('p:nth-of-type(4)').textContent = `预设配置: ${presetConfigs}`;
                topologyDetails.querySelector('svg').innerHTML = svgContent;
            }

            // 初始加载时显示第一个拓扑的详情
            updateTopologyDetails(document.querySelector('.topology-card.selected').dataset.topology);

            const logMessages = [
                "正在加载所选拓扑配置...",
                "正在验证拓扑结构和资源可用性...",
                "正在初始化虚拟机和网络设备...",
                "正在应用基础配置...",
                "拓扑初始化完成！",
                "环境已准备就绪，可以进行部署或故障排除。"
            ];

            initializeButton.addEventListener('click', function() {
                const selectedTopology = document.querySelector('.topology-card.selected h3').textContent;
                terminalArea.innerHTML = `WCM >_ 正在初始化拓扑: ${selectedTopology}<br><br>`;
                let i = 0;
                const interval = setInterval(() => {
                    if (i < logMessages.length) {
                        terminalArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${logMessages[i]}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight; // 滚动到底部
                        i++;
                    } else {
                        clearInterval(interval);
                        terminalArea.innerHTML += `<br>WCM >_ 拓扑初始化过程已完成。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1000); // 每秒输出一条日志
            });
        });
    </script>
</body>
</html>