<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 批量部署</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel label {
            font-size: 1.1em;
            color: #333;
        }

        .control-panel select {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            border-radius: 4px;
            outline: none;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .control-panel select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .batch-deploy-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        .batch-deploy-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .batch-deploy-button:active {
            transform: translateY(0);
        }

        .workstation-list-container {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            animation: none; /* 移除动画 */
        }

        .workstation-list-container h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff;
            text-shadow: none;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .workstation-list {
            list-style: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
            max-height: 250px; /* 限制高度，可滚动 */
            border: 1px dashed #ccc; /* 柔和虚线边框 */
            padding: 10px;
            border-radius: 5px;
        }

        .workstation-list li {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            background-color: #f9f9f9; /* 浅色列表项背景 */
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #eee; /* 柔和边框 */
        }

        .workstation-list li:hover {
            background-color: #f0f0f0;
            border-color: #ddd;
        }

        .workstation-list input[type="checkbox"] {
            margin-right: 10px;
            accent-color: #007bff; /* VMware 蓝色复选框 */
        }

        .workstation-list label {
            flex: 1;
            color: #333;
            font-size: 1em;
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 200px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <label for="batch-environment-select">选择部署环境模板:</label>
            <select id="batch-environment-select">
                <option value="nsm-infra-oam">网络系统管理 - 基础设施编程与自动化运维</option>
                <option value="training-basic">基础训练环境</option>
                <option value="advanced-lab">高级实验环境</option>
            </select>
            <button class="batch-deploy-button">开始批量部署</button>
        </div>

        <div class="workstation-list-container">
            <h2>选择目标工作站 (100+ 可选)</h2>
            <ul class="workstation-list">
                <!-- 模拟100+工作站列表 -->
                <li><input type="checkbox" id="ws1" name="workstation" value="ws1"><label for="ws1">工作站-001 (192.168.1.101)</label></li>
                <li><input type="checkbox" id="ws2" name="workstation" value="ws2"><label for="ws2">工作站-002 (192.168.1.102)</label></li>
                <li><input type="checkbox" id="ws3" name="workstation" value="ws3"><label for="ws3">工作站-003 (192.168.1.103)</label></li>
                <li><input type="checkbox" id="ws4" name="workstation" value="ws4"><label for="ws4">工作站-004 (192.168.1.104)</label></li>
                <li><input type="checkbox" id="ws5" name="workstation" value="ws5"><label for="ws5">工作站-005 (192.168.1.105)</label></li>
                <li><input type="checkbox" id="ws6" name="workstation" value="ws6"><label for="ws6">工作站-006 (192.168.1.106)</label></li>
                <li><input type="checkbox" id="ws7" name="workstation" value="ws7"><label for="ws7">工作站-007 (192.168.1.107)</label></li>
                <li><input type="checkbox" id="ws8" name="workstation" value="ws8"><label for="ws8">工作站-008 (192.168.1.108)</label></li>
                <li><input type="checkbox" id="ws9" name="workstation" value="ws9"><label for="ws9">工作站-009 (192.168.1.109)</label></li>
                <li><input type="checkbox" id="ws10" name="workstation" value="ws10"><label for="ws10">工作站-010 (192.168.1.110)</label></li>
                <li><input type="checkbox" id="ws11" name="workstation" value="ws11"><label for="ws11">工作站-011 (192.168.1.111)</label></li>
                <li><input type="checkbox" id="ws12" name="workstation" value="ws12"><label for="ws12">工作站-012 (192.168.1.112)</label></li>
                <li><input type="checkbox" id="ws13" name="workstation" value="ws13"><label for="ws13">工作站-013 (192.168.1.113)</label></li>
                <li><input type="checkbox" id="ws14" name="workstation" value="ws14"><label for="ws14">工作站-014 (192.168.1.114)</label></li>
                <li><input type="checkbox" id="ws15" name="workstation" value="ws15"><label for="ws15">工作站-015 (192.168.1.115)</label></li>
                <li><input type="checkbox" id="ws16" name="workstation" value="ws16"><label for="ws16">工作站-016 (192.168.1.116)</label></li>
                <li><input type="checkbox" id="ws17" name="workstation" value="ws17"><label for="ws17">工作站-017 (192.168.1.117)</label></li>
                <li><input type="checkbox" id="ws18" name="workstation" value="ws18"><label for="ws18">工作站-018 (192.168.1.118)</label></li>
                <li><input type="checkbox" id="ws19" name="workstation" value="ws19"><label for="ws19">工作站-019 (192.168.1.119)</label></li>
                <li><input type="checkbox" id="ws20" name="workstation" value="ws20"><label for="ws20">工作站-020 (192.168.1.120)</label></li>
                <!-- ... 更多工作站 ... -->
                <li><input type="checkbox" id="ws100" name="workstation" value="ws100"><label for="ws100">工作站-100 (192.168.1.200)</label></li>
            </ul>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待批量部署指令...
            <br>
            <br>
            [2025-07-11 23:55:00] 系统启动，准备就绪。
            <br>
            [2025-07-11 23:55:05] 检测到可用环境模板。
            <br>
            [2025-07-11 23:55:10] 请选择环境模板和目标工作站，然后点击“开始批量部署”按钮。
        </div>

        <!-- 移除 SVG 装饰 -->
        <!-- <svg class="svg-decoration svg-batch" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg">
            <circle cx="125" cy="125" r="100" stroke-dasharray="5 5" />
            <circle cx="125" cy="125" r="80" stroke-dasharray="5 5" />
            <circle cx="125" cy="125" r="60" stroke-dasharray="5 5" />
            <path d="M125 25 L145 55 L125 85 L105 55 Z" fill="#00ffcc" />
            <path d="M125 225 L145 195 L125 165 L105 195 Z" fill="#00ffcc" />
            <path d="M25 125 L55 145 L85 125 L55 105 Z" fill="#00ffcc" />
            <path d="M225 125 L195 145 L165 125 L195 105 Z" fill="#00ffcc" />
            <line x1="125" y1="25" x2="125" y2="225" stroke="#00ccff" stroke-width="1"/>
            <line x1="25" y1="125" x2="225" y2="125" stroke="#00ccff" stroke-width="1"/>
        </svg> -->
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const batchDeployButton = document.querySelector('.batch-deploy-button');
            const environmentSelect = document.getElementById('batch-environment-select');
            const workstationCheckboxes = document.querySelectorAll('.workstation-list input[type="checkbox"]');
            const terminalArea = document.querySelector('.terminal-area');

            batchDeployButton.addEventListener('click', function() {
                const selectedEnv = environmentSelect.options[environmentSelect.selectedIndex].text;
                const selectedWorkstations = Array.from(workstationCheckboxes)
                                                .filter(checkbox => checkbox.checked)
                                                .map(checkbox => checkbox.nextElementSibling.textContent);

                if (selectedWorkstations.length === 0) {
                    terminalArea.innerHTML = `WCM >_ <span style="color: #ff3333;">错误: 请至少选择一个目标工作站进行部署。</span><br>`;
                    terminalArea.scrollTop = terminalArea.scrollHeight;
                    return;
                }

                terminalArea.innerHTML = `WCM >_ 正在为环境 "${selectedEnv}" 准备批量部署到 ${selectedWorkstations.length} 个工作站...<br><br>`;
                terminalArea.scrollTop = terminalArea.scrollHeight;

                let currentWorkstationIndex = 0;
                const deployInterval = setInterval(() => {
                    if (currentWorkstationIndex < selectedWorkstations.length) {
                        const workstation = selectedWorkstations[currentWorkstationIndex];
                        const logMessage = `[${new Date().toLocaleTimeString()}] 正在部署到 ${workstation}...`;
                        terminalArea.innerHTML += `${logMessage}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                        currentWorkstationIndex++;
                    } else {
                        clearInterval(deployInterval);
                        terminalArea.innerHTML += `<br>WCM >_ 所有选定工作站的批量部署过程已完成。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1500); // 每1.5秒模拟部署一个工作站
            });
        });
    </script>
</body>
</html>