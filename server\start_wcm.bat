@echo off
REM WCM Server 启动脚本
REM 用于快速启动 WCM 服务器

echo ========================================
echo WCM (VMware Configuration Manager) Server
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.8+
    pause
    exit /b 1
)

REM 显示 Python 版本
echo Python 版本:
python --version
echo.

REM 检查是否在正确的目录
if not exist "main.py" (
    echo 错误: 未找到 main.py，请确保在 server 目录下运行此脚本
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
python -c "import fastapi, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo 警告: 缺少必要依赖，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查 web 目录
if not exist "web" (
    echo 错误: 未找到 web 目录，请确保 HTML 文件已复制到 web 目录
    pause
    exit /b 1
)

REM 检查关键 HTML 文件
if not exist "web\index.html" (
    echo 错误: 未找到 web\index.html
    pause
    exit /b 1
)

echo 依赖检查完成
echo.

REM 显示启动信息
echo 启动配置:
echo - 服务器地址: http://127.0.0.1:8080
echo - API 文档: http://127.0.0.1:8080/docs
echo - 默认用户: admin / password
echo.

echo 正在启动 WCM 服务器...
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器
python start_server.py

REM 如果服务器异常退出
echo.
echo 服务器已停止
pause
