"""
VMware 配置文件解析器
"""
import os
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
from config import Config


class VMwareConfigParser:
    """VMware配置文件解析器"""
    
    def __init__(self):
        self.inventory_data = {}
        self.preferences_data = {}
        
    def parse_config_file(self, file_path: str) -> Dict[str, str]:
        """
        解析VMware配置文件（.vmls, .ini格式）
        返回键值对字典
        """
        config_data = {}
        
        if not os.path.exists(file_path):
            return config_data
            
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        # 分割键值对
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"')
                        config_data[key] = value
        except Exception as e:
            print(f"解析配置文件错误 {file_path}: {e}")
            
        return config_data
    
    def parse_inventory(self) -> Dict[str, Any]:
        """解析inventory.vmls文件"""
        self.inventory_data = self.parse_config_file(Config.INVENTORY_FILE)
        
        # 解析虚拟机列表
        vms = self._parse_vm_list()
        folders = self._parse_folders()
        
        return {
            "raw_data": self.inventory_data,
            "virtual_machines": vms,
            "folders": folders,
            "vm_count": len(vms),
            "folder_count": len(folders)
        }
    
    def parse_preferences(self) -> Dict[str, Any]:
        """解析preferences.ini文件"""
        self.preferences_data = self.parse_config_file(Config.PREFERENCES_FILE)
        
        # 解析LAN网络配置
        lan_networks = self._parse_lan_networks()
        
        return {
            "raw_data": self.preferences_data,
            "lan_networks": lan_networks,
            "window_settings": self._parse_window_settings(),
            "wizard_settings": self._parse_wizard_settings()
        }
    
    def _parse_vm_list(self) -> List[Dict[str, Any]]:
        """解析虚拟机列表"""
        vms = []
        vm_pattern = re.compile(r'^vmlist(\d+)\.(.+)$')
        
        # 按虚拟机ID分组
        vm_groups = {}
        for key, value in self.inventory_data.items():
            match = vm_pattern.match(key)
            if match:
                vm_id = match.group(1)
                property_name = match.group(2)
                
                if vm_id not in vm_groups:
                    vm_groups[vm_id] = {}
                vm_groups[vm_id][property_name] = value
        
        # 转换为虚拟机对象列表
        for vm_id, properties in vm_groups.items():
            # 跳过文件夹类型
            if properties.get('Type') == '2':
                continue
                
            vm_info = {
                "id": vm_id,
                "display_name": properties.get('DisplayName', ''),
                "config_path": properties.get('config', ''),
                "uuid": properties.get('UUID', ''),
                "state": properties.get('State', 'unknown'),
                "parent_id": properties.get('ParentID', '0'),
                "is_favorite": properties.get('IsFavorite', 'FALSE') == 'TRUE',
                "is_clone": properties.get('IsClone', 'FALSE') == 'TRUE',
                "cfg_version": properties.get('CfgVersion', ''),
                "seq_id": properties.get('SeqID', ''),
                "exists": os.path.exists(properties.get('config', '')) if properties.get('config') else False
            }
            
            # 获取虚拟机详细配置
            if vm_info["config_path"] and vm_info["exists"]:
                vm_config = self._parse_vm_config(vm_info["config_path"])
                vm_info.update(vm_config)
            
            vms.append(vm_info)
        
        return vms
    
    def _parse_folders(self) -> List[Dict[str, Any]]:
        """解析文件夹列表"""
        folders = []
        vm_pattern = re.compile(r'^vmlist(\d+)\.(.+)$')
        
        # 按文件夹ID分组
        folder_groups = {}
        for key, value in self.inventory_data.items():
            match = vm_pattern.match(key)
            if match:
                folder_id = match.group(1)
                property_name = match.group(2)
                
                if folder_id not in folder_groups:
                    folder_groups[folder_id] = {}
                folder_groups[folder_id][property_name] = value
        
        # 转换为文件夹对象列表
        for folder_id, properties in folder_groups.items():
            # 只处理文件夹类型
            if properties.get('Type') == '2':
                folder_info = {
                    "id": folder_id,
                    "display_name": properties.get('DisplayName', ''),
                    "uuid": properties.get('UUID', ''),
                    "parent_id": properties.get('ParentID', '0'),
                    "expanded": properties.get('Expanded', 'FALSE') == 'TRUE',
                    "seq_id": properties.get('SeqID', '')
                }
                folders.append(folder_info)
        
        return folders
    
    def _parse_vm_config(self, config_path: str) -> Dict[str, Any]:
        """解析单个虚拟机的.vmx配置文件"""
        vm_config = {}
        
        try:
            config_data = self.parse_config_file(config_path)
            
            # 解析网卡信息
            network_adapters = self._parse_network_adapters(config_data)

            vm_config.update({
                "guest_os": config_data.get('guestOS', ''),
                "display_name_vmx": config_data.get('displayName', ''),
                "memory_size": config_data.get('memsize', ''),
                "num_vcpus": config_data.get('numvcpus', ''),
                "network_adapters": network_adapters,
                # 保持向后兼容
                "ethernet_present": len(network_adapters) > 0 and network_adapters[0].get('present', False),
                "ethernet_connection_type": network_adapters[0].get('connection_type', '') if network_adapters else '',
                "ethernet_vnet": network_adapters[0].get('vnet', '') if network_adapters else '',
                "tools_sync_time": config_data.get('tools.syncTime', 'FALSE') == 'TRUE',
                "config_file_size": os.path.getsize(config_path) if os.path.exists(config_path) else 0
            })
            
        except Exception as e:
            print(f"解析虚拟机配置文件错误 {config_path}: {e}")
            
        return vm_config

    def _parse_network_adapters(self, config_data: Dict[str, str]) -> List[Dict[str, Any]]:
        """解析网络适配器配置"""
        adapters = []

        # 检查最多10个网卡（ethernet0到ethernet9）
        for i in range(10):
            present_key = f'ethernet{i}.present'
            if present_key in config_data and config_data[present_key].upper() == 'TRUE':
                adapter = {
                    "index": i,
                    "present": True,
                    "connection_type": config_data.get(f'ethernet{i}.connectionType', ''),
                    "vnet": config_data.get(f'ethernet{i}.vnet', ''),
                    "pvn_id": config_data.get(f'ethernet{i}.pvnID', ''),
                    "address_type": config_data.get(f'ethernet{i}.addressType', ''),
                    "generated_address": config_data.get(f'ethernet{i}.generatedAddress', ''),
                    "wake_on_lan": config_data.get(f'ethernet{i}.wakeOnPcktRcv', 'FALSE') == 'TRUE',
                    "link_state_propagation": config_data.get(f'ethernet{i}.linkStatePropagation.enable', 'FALSE') == 'TRUE'
                }
                adapters.append(adapter)

        return adapters

    def _update_network_adapters(self, vm_config: Dict[str, str], adapters: List[Dict[str, Any]]) -> None:
        """更新网络适配器配置"""
        # 首先清除所有现有的网卡配置（最多10个）
        for i in range(10):
            keys_to_remove = []
            for key in vm_config.keys():
                if key.startswith(f'ethernet{i}.'):
                    keys_to_remove.append(key)
            for key in keys_to_remove:
                del vm_config[key]

        # 添加新的网卡配置
        for adapter in adapters:
            index = adapter.get('index', 0)
            if adapter.get('present', True):
                vm_config[f'ethernet{index}.present'] = 'TRUE'

                connection_type = adapter.get('connection_type', 'nat')
                vm_config[f'ethernet{index}.connectionType'] = connection_type

                # 如果是LAN网络，需要设置pvnID
                if connection_type == 'pvn':
                    # 优先使用adapter中的pvn_id，如果没有则根据网络名称查找
                    pvn_id = adapter.get('pvn_id')
                    if not pvn_id:
                        lan_network_name = adapter.get('vnet', '')
                        if lan_network_name:
                            pvn_id = self.get_lan_network_pvn_id(lan_network_name)

                    if pvn_id:
                        vm_config[f'ethernet{index}.pvnID'] = pvn_id
                    else:
                        print(f"警告: 找不到LAN网络的pvnID")

                if 'vnet' in adapter and adapter['vnet']:
                    vm_config[f'ethernet{index}.vnet'] = adapter['vnet']

                if 'address_type' in adapter and adapter['address_type']:
                    vm_config[f'ethernet{index}.addressType'] = adapter['address_type']
                else:
                    vm_config[f'ethernet{index}.addressType'] = 'generated'

                if 'generated_address' in adapter and adapter['generated_address']:
                    vm_config[f'ethernet{index}.generatedAddress'] = adapter['generated_address']

                if adapter.get('wake_on_lan', False):
                    vm_config[f'ethernet{index}.wakeOnPcktRcv'] = 'TRUE'

                if adapter.get('link_state_propagation', False):
                    vm_config[f'ethernet{index}.linkStatePropagation.enable'] = 'TRUE'

    def add_network_adapter(self, vm_id: str, adapter_config: Dict[str, Any]) -> bool:
        """添加网络适配器"""
        try:
            vm_info = self.get_vm_by_id(vm_id)
            if not vm_info or not vm_info.get('config_path'):
                return False

            config_path = vm_info['config_path']
            if not os.path.exists(config_path):
                return False

            # 读取现有配置
            vm_config = self.parse_config_file(config_path)
            current_adapters = self._parse_network_adapters(vm_config)

            # 找到下一个可用的网卡索引
            used_indices = {adapter['index'] for adapter in current_adapters}
            next_index = 0
            while next_index in used_indices and next_index < 10:
                next_index += 1

            if next_index >= 10:
                return False  # 最多支持10个网卡

            # 添加新网卡
            new_adapter = {
                'index': next_index,
                'present': True,
                'connection_type': adapter_config.get('connection_type', 'nat'),
                'vnet': adapter_config.get('vnet', ''),
                'address_type': adapter_config.get('address_type', 'generated'),
                'generated_address': adapter_config.get('generated_address', ''),
                'wake_on_lan': adapter_config.get('wake_on_lan', False),
                'link_state_propagation': adapter_config.get('link_state_propagation', False)
            }

            # 如果是LAN网络，获取并设置pvnID
            if new_adapter['connection_type'] == 'pvn' and new_adapter['vnet']:
                pvn_id = self.get_lan_network_pvn_id(new_adapter['vnet'])
                if pvn_id:
                    new_adapter['pvn_id'] = pvn_id

            current_adapters.append(new_adapter)

            # 更新配置
            self._update_network_adapters(vm_config, current_adapters)
            self._write_vmx_file(config_path, vm_config)

            return True

        except Exception as e:
            print(f"添加网络适配器错误: {e}")
            return False

    def remove_network_adapter(self, vm_id: str, adapter_index: int) -> bool:
        """删除网络适配器"""
        try:
            vm_info = self.get_vm_by_id(vm_id)
            if not vm_info or not vm_info.get('config_path'):
                return False

            config_path = vm_info['config_path']
            if not os.path.exists(config_path):
                return False

            # 读取现有配置
            vm_config = self.parse_config_file(config_path)
            current_adapters = self._parse_network_adapters(vm_config)

            # 移除指定索引的网卡
            updated_adapters = [adapter for adapter in current_adapters if adapter['index'] != adapter_index]

            if len(updated_adapters) == len(current_adapters):
                return False  # 没有找到要删除的网卡

            # 更新配置
            self._update_network_adapters(vm_config, updated_adapters)
            self._write_vmx_file(config_path, vm_config)

            return True

        except Exception as e:
            print(f"删除网络适配器错误: {e}")
            return False

    def _parse_lan_networks(self) -> List[Dict[str, Any]]:
        """解析LAN网络配置"""
        networks = []

        # 获取网络数量
        network_count = int(self.preferences_data.get('pref.namedPVNs.count', '0'))

        for i in range(network_count):
            network_name = self.preferences_data.get(f'pref.namedPVNs{i}.name', '')
            network_id = self.preferences_data.get(f'pref.namedPVNs{i}.pvnID', '')

            if network_name and network_id:
                networks.append({
                    "index": i,
                    "name": network_name,
                    "pvn_id": network_id
                })

        return networks

    def get_lan_network_pvn_id(self, network_name: str) -> Optional[str]:
        """根据LAN网络名称获取pvnID"""
        try:
            preferences = self.parse_preferences()
            lan_networks = preferences.get("lan_networks", [])

            for network in lan_networks:
                if network.get("name") == network_name:
                    return network.get("pvn_id")

            return None
        except Exception as e:
            print(f"获取LAN网络pvnID错误: {e}")
            return None

    def _parse_window_settings(self) -> Dict[str, Any]:
        """解析窗口设置"""
        return {
            "window_count": int(self.preferences_data.get('pref.ws.session.window.count', '0')),
            "sidebar_enabled": self.preferences_data.get('pref.ws.session.window0.sidebar', 'FALSE') == 'TRUE',
            "sidebar_width": self.preferences_data.get('pref.ws.session.window0.sidebar.width', '200'),
            "status_bar": self.preferences_data.get('pref.ws.session.window0.statusBar', 'FALSE') == 'TRUE',
            "maximized": self.preferences_data.get('pref.ws.session.window0.maximized', 'FALSE') == 'TRUE'
        }

    def _parse_wizard_settings(self) -> Dict[str, Any]:
        """解析向导设置"""
        iso_locations = []
        iso_count = int(self.preferences_data.get('vmWizard.isoLocationMRU.count', '0'))

        for i in range(iso_count):
            iso_location = self.preferences_data.get(f'vmWizard.isoLocationMRU{i}.location', '')
            if iso_location:
                iso_locations.append(iso_location)

        return {
            "guest_key": self.preferences_data.get('vmWizard.guestKey', ''),
            "install_media_type": self.preferences_data.get('vmWizard.installMediaType', ''),
            "iso_locations": iso_locations
        }

    def get_vm_by_id(self, vm_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取虚拟机信息"""
        inventory = self.parse_inventory()
        for vm in inventory["virtual_machines"]:
            if vm["id"] == vm_id:
                return vm
        return None

    def get_vm_by_name(self, vm_name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取虚拟机信息"""
        inventory = self.parse_inventory()
        for vm in inventory["virtual_machines"]:
            if vm["display_name"] == vm_name:
                return vm
        return None

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        inventory = self.parse_inventory()
        preferences = self.parse_preferences()

        return {
            "total_vms": inventory["vm_count"],
            "total_folders": inventory["folder_count"],
            "lan_networks": len(preferences["lan_networks"]),
            "config_files_exist": {
                "inventory": os.path.exists(Config.INVENTORY_FILE),
                "preferences": os.path.exists(Config.PREFERENCES_FILE)
            },
            "default_vm_directories": self._get_vm_directories_info()
        }

    def _get_vm_directories_info(self) -> List[Dict[str, Any]]:
        """获取虚拟机目录信息"""
        directories = []

        for path in Config.DEFAULT_VM_PATHS:
            dir_info = {
                "path": path,
                "exists": os.path.exists(path),
                "vm_count": 0
            }

            if dir_info["exists"]:
                try:
                    # 统计目录中的.vmx文件数量
                    vmx_files = list(Path(path).rglob("*.vmx"))
                    dir_info["vm_count"] = len(vmx_files)
                except Exception:
                    pass

            directories.append(dir_info)

        return directories

    def update_vm_config(self, vm_id: str, config_updates: Dict[str, str]) -> bool:
        """更新虚拟机配置"""
        try:
            # 获取虚拟机信息
            vm_info = self.get_vm_by_id(vm_id)
            if not vm_info or not vm_info.get('config_path'):
                return False

            config_path = vm_info['config_path']
            if not os.path.exists(config_path):
                return False

            # 读取现有配置
            vm_config = self.parse_config_file(config_path)

            # 更新配置
            if 'display_name' in config_updates:
                vm_config['displayName'] = config_updates['display_name']
            if 'memory_size' in config_updates:
                vm_config['memsize'] = config_updates['memory_size']
            if 'num_vcpus' in config_updates:
                vm_config['numvcpus'] = config_updates['num_vcpus']

            # 处理网络适配器更新
            if 'network_adapters' in config_updates:
                self._update_network_adapters(vm_config, config_updates['network_adapters'])

            # 保持向后兼容的单网卡更新
            if 'network_type' in config_updates:
                vm_config['ethernet0.connectionType'] = config_updates['network_type']
                vm_config['ethernet0.present'] = 'TRUE'

                # 如果是LAN网络，需要设置相关配置
                if config_updates['network_type'] == 'pvn':
                    if 'lan_network' in config_updates and config_updates['lan_network']:
                        # 设置pvnID
                        pvn_id = self.get_lan_network_pvn_id(config_updates['lan_network'])
                        if pvn_id:
                            vm_config['ethernet0.pvnID'] = pvn_id
                        else:
                            print(f"警告: 找不到LAN网络 '{config_updates['lan_network']}' 的pvnID")

            # 写回配置文件
            self._write_vmx_file(config_path, vm_config)

            # 同时更新inventory中的显示名称
            if 'display_name' in config_updates:
                self._update_inventory_vm_name(vm_id, config_updates['display_name'])

            return True

        except Exception as e:
            print(f"更新虚拟机配置错误: {e}")
            return False

    def delete_vm_from_inventory(self, vm_id: str) -> bool:
        """从inventory中删除虚拟机"""
        try:
            # 读取inventory文件
            inventory_data = self.parse_config_file(Config.INVENTORY_FILE)

            # 删除相关的配置项
            keys_to_delete = []
            for key in inventory_data.keys():
                if key.startswith(f'vmlist{vm_id}.'):
                    keys_to_delete.append(key)

            for key in keys_to_delete:
                del inventory_data[key]

            # 写回文件
            self._write_inventory_file(inventory_data)
            return True

        except Exception as e:
            print(f"删除虚拟机错误: {e}")
            return False

    def move_vm_to_folder(self, vm_id: str, folder_id: str) -> bool:
        """移动虚拟机到指定文件夹"""
        try:
            # 读取inventory文件
            inventory_data = self.parse_config_file(Config.INVENTORY_FILE)

            # 更新虚拟机的父级ID
            parent_key = f'vmlist{vm_id}.ParentID'
            if parent_key in inventory_data:
                inventory_data[parent_key] = folder_id

                # 写回文件
                self._write_inventory_file(inventory_data)
                return True

            return False

        except Exception as e:
            print(f"移动虚拟机错误: {e}")
            return False

    def _write_vmx_file(self, file_path: str, config_data: Dict[str, str]) -> None:
        """写入VMX配置文件"""
        # 备份原文件
        backup_file = file_path + ".backup"
        if os.path.exists(file_path):
            import shutil
            shutil.copy2(file_path, backup_file)

        # 写入新配置
        with open(file_path, 'w', encoding='utf-8') as f:
            for key, value in config_data.items():
                f.write(f'{key} = "{value}"\n')

    def _write_inventory_file(self, inventory_data: Dict[str, str]) -> None:
        """写入inventory文件"""
        # 备份原文件
        backup_file = Config.INVENTORY_FILE + ".backup"
        if os.path.exists(Config.INVENTORY_FILE):
            import shutil
            shutil.copy2(Config.INVENTORY_FILE, backup_file)

        # 写入新配置
        with open(Config.INVENTORY_FILE, 'w', encoding='utf-8') as f:
            f.write('.encoding = "GBK"\n')

            # 按特定顺序写入配置项
            ordered_keys = self._get_ordered_inventory_keys(inventory_data)

            for key in ordered_keys:
                if key in inventory_data and key != '.encoding':
                    value = inventory_data[key]
                    f.write(f'{key} = "{value}"\n')

    def _update_inventory_vm_name(self, vm_id: str, new_name: str) -> None:
        """更新inventory中的虚拟机显示名称"""
        try:
            inventory_data = self.parse_config_file(Config.INVENTORY_FILE)
            display_name_key = f'vmlist{vm_id}.DisplayName'

            if display_name_key in inventory_data:
                inventory_data[display_name_key] = new_name
                self._write_inventory_file(inventory_data)

        except Exception as e:
            print(f"更新inventory虚拟机名称错误: {e}")

    def _get_ordered_inventory_keys(self, inventory_data: Dict[str, str]) -> List[str]:
        """获取有序的inventory配置键列表"""
        ordered_keys = []
        remaining_keys = set(inventory_data.keys())

        # 按vmlist编号排序
        vmlist_keys = [key for key in remaining_keys if key.startswith('vmlist')]
        vmlist_keys.sort(key=lambda x: self._extract_vmlist_number(x))
        ordered_keys.extend(vmlist_keys)
        remaining_keys -= set(vmlist_keys)

        # 添加index相关的键
        index_keys = [key for key in remaining_keys if key.startswith('index')]
        index_keys.sort()
        ordered_keys.extend(index_keys)
        remaining_keys -= set(index_keys)

        # 添加剩余的键
        remaining_keys = sorted(remaining_keys)
        ordered_keys.extend(remaining_keys)

        return ordered_keys

    def _extract_vmlist_number(self, key: str) -> int:
        """从vmlist键中提取数字"""
        try:
            import re
            match = re.search(r'vmlist(\d+)', key)
            return int(match.group(1)) if match else 0
        except:
            return 0
