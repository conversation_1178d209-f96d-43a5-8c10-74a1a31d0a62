"""
WCM (VMware Configuration Manager) - 服务器端主应用
基于FastAPI的VMware Workstation管理系统后端服务
"""
from fastapi import FastAPI, HTTPException, Request, Depends, status
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import os
import sys
import hashlib
import jwt
from datetime import datetime, timedelta, timezone
import json
import asyncio
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加client目录到Python路径，以便导入client模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'client'))

try:
    from config import ServerConfig
except ImportError:
    # 如果无法导入ServerConfig，使用默认配置
    class ServerConfig:
        HOST = "127.0.0.1"
        PORT = 8080
        DEBUG = True
        SECRET_KEY = "wcm_secret_key_2024"
        ALGORITHM = "HS256"
        ACCESS_TOKEN_EXPIRE_MINUTES = 30
        WEB_DIR = os.path.join(os.path.dirname(__file__), "web")

try:
    from vmware_detector import VMwareDetector
    from vmware_config_parser import VMwareConfigParser
    from vmware_network_manager import VMwareNetworkManager
except ImportError as e:
    logger.warning(f"无法导入VMware模块: {e}")
    # 创建模拟类以防止导入错误
    class VMwareDetector:
        def get_vmware_info(self): return {"installed": False, "version": "未知"}
        def is_vmware_running(self): return False
        def get_default_vm_directories(self): return []

    class VMwareConfigParser:
        def parse_inventory(self): return {"vms": []}
        def get_vm_by_id(self, vm_id): return None
        def get_vm_by_name(self, vm_name): return None
        def get_system_status(self): return {"inventory_exists": False}

    class VMwareNetworkManager:
        def get_lan_networks(self): return []

# 创建FastAPI应用
app = FastAPI(
    title="WCM - VMware Configuration Manager",
    description="VMware Workstation管理系统后端API",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化VMware组件
vmware_detector = VMwareDetector()
config_parser = VMwareConfigParser()
network_manager = VMwareNetworkManager()

# JWT配置
SECRET_KEY = "wcm_secret_key_2024"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# HTTP Bearer认证
security = HTTPBearer()

# 用户数据（实际应用中应该使用数据库）
USERS_DB = {
    "admin": {
        "username": "admin",
        "password": "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",  # "password" 的SHA256
        "role": "admin"
    }
}

# Pydantic模型
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class DeployRequest(BaseModel):
    vm_name: str
    template_path: Optional[str] = None
    config: Optional[Dict[str, Any]] = None

class RestoreRequest(BaseModel):
    vm_name: str
    snapshot_name: Optional[str] = None
    backup_path: Optional[str] = None

class TopologyRequest(BaseModel):
    topology_type: str
    network_config: Dict[str, Any]

class BatchDeployRequest(BaseModel):
    vms: List[Dict[str, Any]]
    parallel: bool = True

class ScoringRequest(BaseModel):
    vm_names: List[str]
    criteria: Dict[str, Any]

class TroubleshootRequest(BaseModel):
    issue_type: str
    vm_name: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

# 工具函数
def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return hash_password(plain_password) == hashed_password

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    user = USERS_DB.get(username)
    if user is None:
        raise credentials_exception
    return user

# 静态文件服务
try:
    app.mount("/static", StaticFiles(directory=ServerConfig.WEB_DIR), name="static")
except Exception as e:
    logger.warning(f"无法挂载静态文件目录: {e}")

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """主页 - 登录页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "index.html"))

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """仪表板页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "dashboard.html"))

@app.get("/deploy", response_class=HTMLResponse)
async def deploy_page():
    """一键部署页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "deploy.html"))

@app.get("/restore", response_class=HTMLResponse)
async def restore_page():
    """环境恢复页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "restore.html"))

@app.get("/topology", response_class=HTMLResponse)
async def topology_page():
    """拓扑管理页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "topology.html"))

@app.get("/batch_deploy", response_class=HTMLResponse)
async def batch_deploy_page():
    """批量部署页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "batch_deploy.html"))

@app.get("/scoring", response_class=HTMLResponse)
async def scoring_page():
    """自动化评分页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "scoring.html"))

@app.get("/troubleshooting", response_class=HTMLResponse)
async def troubleshooting_page():
    """故障排除页面"""
    return FileResponse(os.path.join(ServerConfig.WEB_DIR, "troubleshooting.html"))

# 认证API
@app.post("/api/auth/login", response_model=Token)
async def login(login_request: LoginRequest):
    """用户登录"""
    user = USERS_DB.get(login_request.username)
    if not user or not verify_password(login_request.password, user["password"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/auth/me")
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {"username": current_user["username"], "role": current_user["role"]}

# 系统信息API（继承自client/main.py）
@app.get("/api/system/info")
async def get_system_info(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取系统信息"""
    try:
        vmware_info = vmware_detector.get_vmware_info()
        system_status = config_parser.get_system_status()
        
        return {
            "vmware_installation": vmware_info,
            "system_status": system_status,
            "vmware_running": vmware_detector.is_vmware_running(),
            "default_vm_directories": vmware_detector.get_default_vm_directories()
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

# 虚拟机管理API（继承自client/main.py）
@app.get("/api/vms")
async def get_virtual_machines(current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取虚拟机列表"""
    try:
        inventory = config_parser.parse_inventory()
        return inventory
    except Exception as e:
        logger.error(f"获取虚拟机列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取虚拟机列表失败: {str(e)}")

@app.get("/api/vms/{vm_id}")
async def get_virtual_machine(vm_id: str, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """获取指定虚拟机信息"""
    try:
        vm_info = config_parser.get_vm_by_id(vm_id)
        if not vm_info:
            raise HTTPException(status_code=404, detail=f"虚拟机 ID {vm_id} 不存在")
        return vm_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取虚拟机信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取虚拟机信息失败: {str(e)}")

# 网络管理API
@app.get("/api/networks")
async def get_lan_networks(current_user: dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """获取LAN网络列表"""
    try:
        return network_manager.get_lan_networks()
    except Exception as e:
        logger.error(f"获取网络列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取网络列表失败: {str(e)}")

# 一键部署API
@app.post("/api/deploy")
async def deploy_vm(deploy_request: DeployRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """一键部署虚拟机"""
    try:
        logger.info(f"开始部署虚拟机: {deploy_request.vm_name}")

        # 这里实现具体的部署逻辑
        # 1. 检查模板是否存在
        # 2. 创建虚拟机配置
        # 3. 启动虚拟机

        result = {
            "success": True,
            "message": f"虚拟机 {deploy_request.vm_name} 部署成功",
            "vm_name": deploy_request.vm_name,
            "deployment_id": f"deploy_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            "status": "deployed"
        }

        logger.info(f"虚拟机部署完成: {deploy_request.vm_name}")
        return result

    except Exception as e:
        logger.error(f"部署虚拟机失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"部署虚拟机失败: {str(e)}")

# 环境恢复API
@app.post("/api/restore")
async def restore_vm(restore_request: RestoreRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """恢复虚拟机环境"""
    try:
        logger.info(f"开始恢复虚拟机环境: {restore_request.vm_name}")

        # 这里实现具体的恢复逻辑
        # 1. 检查快照或备份是否存在
        # 2. 停止虚拟机
        # 3. 恢复到指定状态
        # 4. 重新启动虚拟机

        result = {
            "success": True,
            "message": f"虚拟机 {restore_request.vm_name} 环境恢复成功",
            "vm_name": restore_request.vm_name,
            "restore_id": f"restore_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            "status": "restored"
        }

        logger.info(f"虚拟机环境恢复完成: {restore_request.vm_name}")
        return result

    except Exception as e:
        logger.error(f"恢复虚拟机环境失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复虚拟机环境失败: {str(e)}")

# 拓扑管理API
@app.post("/api/topology/initialize")
async def initialize_topology(topology_request: TopologyRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """初始化网络拓扑"""
    try:
        logger.info(f"开始初始化网络拓扑: {topology_request.topology_type}")

        # 这里实现具体的拓扑初始化逻辑
        # 1. 创建网络配置
        # 2. 设置虚拟交换机
        # 3. 配置网络适配器

        result = {
            "success": True,
            "message": f"网络拓扑 {topology_request.topology_type} 初始化成功",
            "topology_type": topology_request.topology_type,
            "topology_id": f"topo_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            "status": "initialized"
        }

        logger.info(f"网络拓扑初始化完成: {topology_request.topology_type}")
        return result

    except Exception as e:
        logger.error(f"初始化网络拓扑失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"初始化网络拓扑失败: {str(e)}")

@app.get("/api/topology/templates")
async def get_topology_templates(current_user: dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """获取拓扑模板列表"""
    try:
        templates = [
            {
                "id": "simple_lan",
                "name": "简单局域网",
                "description": "基本的局域网拓扑，包含一个交换机和多个主机",
                "nodes": 5,
                "complexity": "简单"
            },
            {
                "id": "routed_network",
                "name": "路由网络",
                "description": "包含路由器的多子网网络拓扑",
                "nodes": 8,
                "complexity": "中等"
            },
            {
                "id": "enterprise_network",
                "name": "企业网络",
                "description": "复杂的企业级网络拓扑，包含多层交换和安全设备",
                "nodes": 15,
                "complexity": "复杂"
            }
        ]
        return templates
    except Exception as e:
        logger.error(f"获取拓扑模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取拓扑模板失败: {str(e)}")

# 批量部署API
@app.post("/api/batch-deploy")
async def batch_deploy_vms(batch_request: BatchDeployRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """批量部署虚拟机"""
    try:
        logger.info(f"开始批量部署 {len(batch_request.vms)} 个虚拟机")

        results = []
        batch_id = f"batch_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"

        if batch_request.parallel:
            # 并行部署
            tasks = []
            for vm_config in batch_request.vms:
                task = asyncio.create_task(deploy_single_vm(vm_config))
                tasks.append(task)

            deploy_results = await asyncio.gather(*tasks, return_exceptions=True)

            for i, result in enumerate(deploy_results):
                if isinstance(result, Exception):
                    results.append({
                        "vm_name": batch_request.vms[i].get("name", f"vm_{i}"),
                        "success": False,
                        "error": str(result)
                    })
                else:
                    results.append(result)
        else:
            # 串行部署
            for vm_config in batch_request.vms:
                try:
                    result = await deploy_single_vm(vm_config)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "vm_name": vm_config.get("name", "unknown"),
                        "success": False,
                        "error": str(e)
                    })

        success_count = sum(1 for r in results if r.get("success", False))

        return {
            "success": True,
            "batch_id": batch_id,
            "total": len(batch_request.vms),
            "success_count": success_count,
            "failed_count": len(batch_request.vms) - success_count,
            "results": results,
            "message": f"批量部署完成，成功: {success_count}, 失败: {len(batch_request.vms) - success_count}"
        }

    except Exception as e:
        logger.error(f"批量部署失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量部署失败: {str(e)}")

async def deploy_single_vm(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """部署单个虚拟机（内部函数）"""
    vm_name = vm_config.get("name", "unknown")
    try:
        # 模拟部署过程
        await asyncio.sleep(1)  # 模拟部署时间

        return {
            "vm_name": vm_name,
            "success": True,
            "message": f"虚拟机 {vm_name} 部署成功",
            "deployment_time": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        return {
            "vm_name": vm_name,
            "success": False,
            "error": str(e)
        }

# 自动化评分API
@app.post("/api/scoring/evaluate")
async def evaluate_vms(scoring_request: ScoringRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """自动化评分虚拟机配置"""
    try:
        logger.info(f"开始评分 {len(scoring_request.vm_names)} 个虚拟机")

        results = []
        total_score = 0

        for vm_name in scoring_request.vm_names:
            try:
                # 获取虚拟机信息
                vm_info = config_parser.get_vm_by_name(vm_name)
                if not vm_info:
                    results.append({
                        "vm_name": vm_name,
                        "score": 0,
                        "max_score": 100,
                        "details": {"error": "虚拟机不存在"},
                        "status": "error"
                    })
                    continue

                # 评分逻辑
                score_details = evaluate_vm_config(vm_info, scoring_request.criteria)
                results.append({
                    "vm_name": vm_name,
                    "score": score_details["score"],
                    "max_score": score_details["max_score"],
                    "details": score_details["details"],
                    "status": "completed"
                })
                total_score += score_details["score"]

            except Exception as e:
                results.append({
                    "vm_name": vm_name,
                    "score": 0,
                    "max_score": 100,
                    "details": {"error": str(e)},
                    "status": "error"
                })

        average_score = total_score / len(scoring_request.vm_names) if scoring_request.vm_names else 0

        return {
            "success": True,
            "evaluation_id": f"eval_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            "total_vms": len(scoring_request.vm_names),
            "average_score": round(average_score, 2),
            "results": results,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"自动化评分失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"自动化评分失败: {str(e)}")

def evaluate_vm_config(vm_info: Dict[str, Any], criteria: Dict[str, Any]) -> Dict[str, Any]:
    """评估虚拟机配置（内部函数）"""
    score = 0
    max_score = 100
    details = {}

    # 内存配置评分 (30分)
    memory_size = vm_info.get("memory_size", "0")
    if memory_size:
        memory_mb = int(memory_size) if memory_size.isdigit() else 0
        if memory_mb >= 2048:
            score += 30
            details["memory"] = {"score": 30, "status": "优秀", "value": f"{memory_mb}MB"}
        elif memory_mb >= 1024:
            score += 20
            details["memory"] = {"score": 20, "status": "良好", "value": f"{memory_mb}MB"}
        else:
            score += 10
            details["memory"] = {"score": 10, "status": "需要改进", "value": f"{memory_mb}MB"}

    # CPU配置评分 (20分)
    num_vcpus = vm_info.get("num_vcpus", "1")
    vcpu_count = int(num_vcpus) if num_vcpus.isdigit() else 1
    if vcpu_count >= 4:
        score += 20
        details["cpu"] = {"score": 20, "status": "优秀", "value": f"{vcpu_count} vCPU"}
    elif vcpu_count >= 2:
        score += 15
        details["cpu"] = {"score": 15, "status": "良好", "value": f"{vcpu_count} vCPU"}
    else:
        score += 10
        details["cpu"] = {"score": 10, "status": "基本", "value": f"{vcpu_count} vCPU"}

    # 网络配置评分 (30分)
    network_adapters = vm_info.get("network_adapters", [])
    if len(network_adapters) >= 2:
        score += 30
        details["network"] = {"score": 30, "status": "优秀", "value": f"{len(network_adapters)} 个网络适配器"}
    elif len(network_adapters) == 1:
        score += 20
        details["network"] = {"score": 20, "status": "良好", "value": "1 个网络适配器"}
    else:
        score += 0
        details["network"] = {"score": 0, "status": "缺失", "value": "无网络适配器"}

    # 显示名称评分 (20分)
    display_name = vm_info.get("display_name", "")
    if display_name and len(display_name) > 3:
        score += 20
        details["naming"] = {"score": 20, "status": "优秀", "value": display_name}
    else:
        score += 5
        details["naming"] = {"score": 5, "status": "需要改进", "value": display_name or "未设置"}

    return {
        "score": score,
        "max_score": max_score,
        "details": details
    }

# 故障排除API
@app.post("/api/troubleshoot/diagnose")
async def diagnose_issue(troubleshoot_request: TroubleshootRequest, current_user: dict = Depends(get_current_user)) -> Dict[str, Any]:
    """诊断系统问题"""
    try:
        logger.info(f"开始诊断问题: {troubleshoot_request.issue_type}")

        diagnosis_results = []

        # 根据问题类型进行诊断
        if troubleshoot_request.issue_type == "vm_startup":
            diagnosis_results = diagnose_vm_startup_issues(troubleshoot_request.vm_name)
        elif troubleshoot_request.issue_type == "network_connectivity":
            diagnosis_results = diagnose_network_issues(troubleshoot_request.vm_name)
        elif troubleshoot_request.issue_type == "performance":
            diagnosis_results = diagnose_performance_issues(troubleshoot_request.vm_name)
        elif troubleshoot_request.issue_type == "system_general":
            diagnosis_results = diagnose_system_issues()
        else:
            diagnosis_results = [{"type": "error", "message": "未知的问题类型"}]

        # 生成解决方案
        solutions = generate_solutions(troubleshoot_request.issue_type, diagnosis_results)

        return {
            "success": True,
            "issue_type": troubleshoot_request.issue_type,
            "vm_name": troubleshoot_request.vm_name,
            "diagnosis_id": f"diag_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            "diagnosis_results": diagnosis_results,
            "solutions": solutions,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"故障诊断失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"故障诊断失败: {str(e)}")

def diagnose_vm_startup_issues(vm_name: Optional[str]) -> List[Dict[str, Any]]:
    """诊断虚拟机启动问题"""
    results = []

    try:
        # 检查VMware服务状态
        if not vmware_detector.is_vmware_running():
            results.append({
                "type": "error",
                "category": "service",
                "message": "VMware Workstation服务未运行",
                "severity": "high"
            })
        else:
            results.append({
                "type": "info",
                "category": "service",
                "message": "VMware Workstation服务运行正常",
                "severity": "low"
            })

        # 检查虚拟机配置
        if vm_name:
            vm_info = config_parser.get_vm_by_name(vm_name)
            if not vm_info:
                results.append({
                    "type": "error",
                    "category": "config",
                    "message": f"虚拟机 '{vm_name}' 不存在或配置文件损坏",
                    "severity": "high"
                })
            else:
                results.append({
                    "type": "info",
                    "category": "config",
                    "message": f"虚拟机 '{vm_name}' 配置文件正常",
                    "severity": "low"
                })

        # 检查系统资源
        results.append({
            "type": "info",
            "category": "resources",
            "message": "系统资源检查完成",
            "severity": "low"
        })

    except Exception as e:
        results.append({
            "type": "error",
            "category": "system",
            "message": f"诊断过程中发生错误: {str(e)}",
            "severity": "medium"
        })

    return results

def diagnose_network_issues(vm_name: Optional[str]) -> List[Dict[str, Any]]:
    """诊断网络连接问题"""
    results = []

    try:
        # 检查网络配置
        networks = network_manager.get_lan_networks()
        if not networks:
            results.append({
                "type": "warning",
                "category": "network",
                "message": "未找到可用的LAN网络",
                "severity": "medium"
            })
        else:
            results.append({
                "type": "info",
                "category": "network",
                "message": f"找到 {len(networks)} 个LAN网络",
                "severity": "low"
            })

        # 检查虚拟机网络适配器
        if vm_name:
            vm_info = config_parser.get_vm_by_name(vm_name)
            if vm_info:
                adapters = vm_info.get("network_adapters", [])
                if not adapters:
                    results.append({
                        "type": "error",
                        "category": "adapter",
                        "message": f"虚拟机 '{vm_name}' 没有配置网络适配器",
                        "severity": "high"
                    })
                else:
                    results.append({
                        "type": "info",
                        "category": "adapter",
                        "message": f"虚拟机 '{vm_name}' 有 {len(adapters)} 个网络适配器",
                        "severity": "low"
                    })

    except Exception as e:
        results.append({
            "type": "error",
            "category": "system",
            "message": f"网络诊断过程中发生错误: {str(e)}",
            "severity": "medium"
        })

    return results

def diagnose_performance_issues(vm_name: Optional[str]) -> List[Dict[str, Any]]:
    """诊断性能问题"""
    results = []

    try:
        if vm_name:
            vm_info = config_parser.get_vm_by_name(vm_name)
            if vm_info:
                # 检查内存配置
                memory_size = vm_info.get("memory_size", "0")
                memory_mb = int(memory_size) if memory_size.isdigit() else 0

                if memory_mb < 1024:
                    results.append({
                        "type": "warning",
                        "category": "memory",
                        "message": f"虚拟机内存配置较低: {memory_mb}MB",
                        "severity": "medium"
                    })
                else:
                    results.append({
                        "type": "info",
                        "category": "memory",
                        "message": f"虚拟机内存配置: {memory_mb}MB",
                        "severity": "low"
                    })

                # 检查CPU配置
                num_vcpus = vm_info.get("num_vcpus", "1")
                vcpu_count = int(num_vcpus) if num_vcpus.isdigit() else 1

                if vcpu_count < 2:
                    results.append({
                        "type": "warning",
                        "category": "cpu",
                        "message": f"虚拟机CPU配置较低: {vcpu_count} vCPU",
                        "severity": "medium"
                    })
                else:
                    results.append({
                        "type": "info",
                        "category": "cpu",
                        "message": f"虚拟机CPU配置: {vcpu_count} vCPU",
                        "severity": "low"
                    })

    except Exception as e:
        results.append({
            "type": "error",
            "category": "system",
            "message": f"性能诊断过程中发生错误: {str(e)}",
            "severity": "medium"
        })

    return results

def diagnose_system_issues() -> List[Dict[str, Any]]:
    """诊断系统问题"""
    results = []

    try:
        # 检查VMware安装
        vmware_info = vmware_detector.get_vmware_info()
        if vmware_info.get("installed", False):
            results.append({
                "type": "info",
                "category": "installation",
                "message": f"VMware Workstation已安装，版本: {vmware_info.get('version', '未知')}",
                "severity": "low"
            })
        else:
            results.append({
                "type": "error",
                "category": "installation",
                "message": "VMware Workstation未安装或安装不完整",
                "severity": "high"
            })

        # 检查配置文件
        system_status = config_parser.get_system_status()
        if system_status.get("inventory_exists", False):
            results.append({
                "type": "info",
                "category": "config",
                "message": "虚拟机清单文件存在",
                "severity": "low"
            })
        else:
            results.append({
                "type": "warning",
                "category": "config",
                "message": "虚拟机清单文件不存在",
                "severity": "medium"
            })

    except Exception as e:
        results.append({
            "type": "error",
            "category": "system",
            "message": f"系统诊断过程中发生错误: {str(e)}",
            "severity": "medium"
        })

    return results

def generate_solutions(issue_type: str, diagnosis_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """根据诊断结果生成解决方案"""
    solutions = []

    for result in diagnosis_results:
        if result["type"] == "error":
            if result["category"] == "service":
                solutions.append({
                    "priority": "high",
                    "title": "启动VMware服务",
                    "description": "请启动VMware Workstation服务",
                    "steps": [
                        "打开服务管理器 (services.msc)",
                        "找到VMware相关服务",
                        "右键点击并选择'启动'"
                    ]
                })
            elif result["category"] == "config":
                solutions.append({
                    "priority": "high",
                    "title": "修复配置文件",
                    "description": "检查并修复虚拟机配置文件",
                    "steps": [
                        "检查虚拟机文件路径是否正确",
                        "验证.vmx配置文件完整性",
                        "重新添加虚拟机到清单"
                    ]
                })
        elif result["type"] == "warning":
            if result["category"] == "memory":
                solutions.append({
                    "priority": "medium",
                    "title": "增加虚拟机内存",
                    "description": "建议增加虚拟机内存配置",
                    "steps": [
                        "关闭虚拟机",
                        "编辑虚拟机设置",
                        "增加内存分配至少到2GB"
                    ]
                })
            elif result["category"] == "cpu":
                solutions.append({
                    "priority": "medium",
                    "title": "增加虚拟机CPU",
                    "description": "建议增加虚拟机CPU核心数",
                    "steps": [
                        "关闭虚拟机",
                        "编辑虚拟机设置",
                        "增加处理器核心数至少到2个"
                    ]
                })

    return solutions

@app.get("/api/troubleshoot/common-issues")
async def get_common_issues(current_user: dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """获取常见问题列表"""
    common_issues = [
        {
            "id": "vm_startup",
            "title": "虚拟机启动问题",
            "description": "虚拟机无法启动或启动缓慢",
            "category": "startup"
        },
        {
            "id": "network_connectivity",
            "title": "网络连接问题",
            "description": "虚拟机网络连接异常或无法访问网络",
            "category": "network"
        },
        {
            "id": "performance",
            "title": "性能问题",
            "description": "虚拟机运行缓慢或资源不足",
            "category": "performance"
        },
        {
            "id": "system_general",
            "title": "系统问题",
            "description": "VMware Workstation系统级问题",
            "category": "system"
        }
    ]
    return common_issues

@app.get("/api/health")
async def health_check() -> Dict[str, str]:
    """健康检查"""
    return {"status": "healthy", "message": "WCM服务器运行正常", "timestamp": datetime.now(timezone.utc).isoformat()}

# 错误处理
@app.exception_handler(404)
async def not_found_handler(_: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={"error": "资源未找到", "detail": str(exc.detail)}
    )

@app.exception_handler(500)
async def internal_error_handler(_: Request, exc: HTTPException):
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误", "detail": str(exc.detail)}
    )

if __name__ == "__main__":
    import uvicorn
    print(f"启动WCM服务器...")
    print(f"访问地址: http://{ServerConfig.HOST}:{ServerConfig.PORT}")
    print(f"API文档: http://{ServerConfig.HOST}:{ServerConfig.PORT}/docs")

    uvicorn.run(
        "main:app",
        host=ServerConfig.HOST,
        port=ServerConfig.PORT,
        reload=ServerConfig.DEBUG
    )
