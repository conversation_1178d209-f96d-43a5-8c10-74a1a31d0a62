# VMware Workstation 管理器

一个用Python和FastAPI开发的Windows客户端，用于管理VMware Workstation虚拟机和网络配置。

## 功能特性

### 🖥️ 系统信息
- 自动检测VMware Workstation安装目录
- 显示VMware版本信息和运行状态
- 获取虚拟机数量和默认安装目录

### 🔧 虚拟机管理
- 读取和解析`inventory.vmls`文件
- 获取所有虚拟机的详细配置信息
- 显示虚拟机状态（正常/文件缺失）
- **编辑虚拟机配置**（显示名称、内存、CPU、网络类型）
- **删除虚拟机**（从inventory中移除）
- **移动虚拟机**到指定文件夹
- 解析虚拟机配置文件（.vmx）获取详细信息：
  - 操作系统类型
  - 内存大小
  - CPU核心数
  - 网络配置
  - 工具同步设置

### 🌐 LAN网络管理
- 读取和解析`preferences.ini`文件
- 显示所有LAN网络配置
- 创建新的LAN网络
- 删除现有LAN网络
- 重命名LAN网络
- 自动生成PVN ID



### 🌐 Web界面
- **简约现代的设计风格**
- **三标签页导航**（概览、虚拟机、网络）
- 响应式设计，支持移动设备
- 实时显示系统信息和统计数据
- 完整的虚拟机管理界面
- 网络管理界面（创建/删除网络）
- 模态框式的编辑和创建界面
- **LAN网络选择支持**（编辑虚拟机时可选择专用网络）

### 📋 RESTful API
- 完整的API文档（FastAPI自动生成）
- 系统信息API
- 虚拟机管理API（CRUD操作）
- 网络管理API
- 文件夹管理API
- 配置文件访问API

## 安装和使用

### 前提条件
- Windows 10/11
- Python 3.8+
- VMware Workstation 已安装

### 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
```bash
py -m pip install -r requirements.txt
```

3. 启动应用：
```bash
py main.py
```

4. 打开浏览器访问：
- Web界面: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

## API 端点

### 系统信息
- `GET /api/system/info` - 获取系统和VMware安装信息
- `GET /api/health` - 健康检查

### 虚拟机管理
- `GET /api/vms` - 获取所有虚拟机列表
- `GET /api/vms/{vm_id}` - 根据ID获取虚拟机信息
- `GET /api/vms/name/{vm_name}` - 根据名称获取虚拟机信息
- `PUT /api/vms/{vm_id}` - 更新虚拟机配置（支持LAN网络选择）
- `DELETE /api/vms/{vm_id}` - 删除虚拟机

### 网络管理
- `GET /api/networks` - 获取所有LAN网络
- `POST /api/networks` - 创建新的LAN网络
- `DELETE /api/networks/{network_name}` - 删除LAN网络
- `PUT /api/networks/rename` - 重命名LAN网络

### 配置文件
- `GET /api/config/inventory` - 获取inventory配置
- `GET /api/config/preferences` - 获取preferences配置

## 配置文件路径

应用会自动读取以下VMware配置文件：
- `C:\Users\<USER>\AppData\Roaming\VMware\inventory.vmls`
- `C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini`

## 项目结构

```
WCM/
├── main.py                    # FastAPI主应用（精简版）
├── config.py                  # 配置文件
├── vmware_detector.py         # VMware安装检测模块
├── vmware_config_parser.py    # 配置文件解析器（支持LAN网络）
├── vmware_network_manager.py  # 网络管理模块
├── requirements.txt           # 依赖包列表
├── templates/
│   └── index.html            # 简约风格Web界面
├── test_api.py               # API测试脚本
└── README.md                 # 说明文档
```

## 使用示例

### 创建LAN网络
```bash
curl -X POST "http://127.0.0.1:8000/api/networks" \
     -H "Content-Type: application/json" \
     -d '{"name": "新网络名称"}'
```

### 删除LAN网络
```bash
curl -X DELETE "http://127.0.0.1:8000/api/networks/网络名称"
```

### 重命名LAN网络
```bash
curl -X PUT "http://127.0.0.1:8000/api/networks/rename" \
     -H "Content-Type: application/json" \
     -d '{"old_name": "旧名称", "new_name": "新名称"}'
```

### 编辑虚拟机配置
```bash
curl -X PUT "http://127.0.0.1:8000/api/vms/2" \
     -H "Content-Type: application/json" \
     -d '{"display_name": "新名称", "memory_size": "4096", "num_vcpus": "2"}'
```

### 设置虚拟机为LAN网络
```bash
curl -X PUT "http://127.0.0.1:8000/api/vms/2" \
     -H "Content-Type: application/json" \
     -d '{"network_type": "pvn", "lan_network": "LAN1"}'
```

### 删除虚拟机
```bash
curl -X DELETE "http://127.0.0.1:8000/api/vms/2"
```

## 注意事项

1. **备份重要文件**: 应用会修改VMware配置文件，建议先备份
2. **管理员权限**: 某些操作可能需要管理员权限
3. **VMware关闭**: 修改配置时建议关闭VMware Workstation
4. **编码支持**: 支持中文文件名和路径

## 故障排除

### 常见问题

1. **VMware未检测到**
   - 确认VMware Workstation已正确安装
   - 检查安装路径是否在默认位置

2. **配置文件读取失败**
   - 确认配置文件路径存在
   - 检查文件权限

3. **网络创建失败**
   - 确认网络名称不重复
   - 检查preferences.ini文件权限

## 技术栈

- **后端**: FastAPI, Python 3.8+
- **前端**: HTML5, CSS3, JavaScript (Vanilla)
- **依赖**: uvicorn, pydantic, jinja2, psutil

## 许可证

本项目仅供学习和个人使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
