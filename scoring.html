<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 自动化评分</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel label {
            font-size: 1.1em;
            color: #333;
        }

        .control-panel select {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            border-radius: 4px;
            outline: none;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .control-panel select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .score-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        .score-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .score-button:active {
            transform: translateY(0);
        }

        .scoring-results-container {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            flex: 1;
            display: flex;
            flex-direction: column;
            animation: none; /* 移除动画 */
        }

        .scoring-results-container h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff;
            text-shadow: none;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .results-table th, .results-table td {
            border: 1px solid #ddd; /* 柔和边框 */
            padding: 10px;
            text-align: left;
            font-size: 0.9em;
        }

        .results-table th {
            background-color: #f0f0f0; /* 浅灰色表头背景 */
            color: #333;
            text-shadow: none;
        }

        .results-table td {
            background-color: #fff;
            color: #555;
        }

        .results-table .status-pass {
            color: #28a745; /* 绿色表示通过 */
            font-weight: bold;
        }

        .results-table .status-fail {
            color: #dc3545; /* 红色表示失败 */
            font-weight: bold;
            text-shadow: none;
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 200px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <label for="scoring-module-select">选择评分模块:</label>
            <select id="scoring-module-select">
                <option value="nsm-infra-oam">网络系统管理 - 基础设施编程与自动化运维</option>
                <option value="network-config">网络配置与故障排除</option>
                <option value="security-hardening">系统安全加固</option>
            </select>
            <button class="score-button">开始自动化评分</button>
        </div>

        <div class="scoring-results-container">
            <h2>评分结果</h2>
            <table class="results-table" id="scoring-results-table">
                <thead>
                    <tr>
                        <th>评估项</th>
                        <th>状态</th>
                        <th>得分</th>
                        <th>详细反馈</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 评分结果将动态加载到这里 -->
                    <tr>
                        <td>等待评分...</td>
                        <td>N/A</td>
                        <td>N/A</td>
                        <td>请选择一个模块并点击“开始自动化评分”按钮。</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待评分指令...
            <br>
            <br>
            [2025-07-11 23:58:00] 系统启动，准备就绪。
            <br>
            [2025-07-11 23:58:05] 检测到可用评分模块。
            <br>
            [2025-07-11 23:58:10] 请选择一个模块并点击“开始自动化评分”按钮。
        </div>

        <!-- 移除 SVG 装饰 -->
        <!-- <svg class="svg-decoration svg-score" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <circle cx="100" cy="100" r="90" stroke-dasharray="10 5" />
            <line x1="100" y1="10" x2="100" y2="190" stroke="#ff00ff" stroke-width="1"/>
            <line x1="10" y1="100" x2="190" y2="100" stroke="#ff00ff" stroke-width="1"/>
            <path d="M100 100 L150 50 L100 0 L50 50 Z" fill="#00ffcc" opacity="0.5"/>
            <path d="M100 100 L150 150 L100 200 L50 150 Z" fill="#00ccff" opacity="0.5"/>
            <circle cx="100" cy="100" r="20" fill="#0a0a0a" stroke="#00ffcc" stroke-width="2"/>
            <text x="100" y="105" text-anchor="middle" fill="#ff00ff" font-size="18" font-weight="bold">SCORE</text>
        </svg> -->
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const scoreButton = document.querySelector('.score-button');
            const scoringModuleSelect = document.getElementById('scoring-module-select');
            const terminalArea = document.querySelector('.terminal-area');
            const resultsTableBody = document.querySelector('#scoring-results-table tbody');

            const scoringData = {
                'nsm-infra-oam': [
                    { item: '虚拟机网络连通性', status: '通过', score: '10/10', feedback: '所有虚拟机网络配置正确，互相可达。' },
                    { item: 'SSH服务配置', status: '失败', score: '0/10', feedback: 'SSH服务未启动或端口错误。' },
                    { item: 'Web服务器部署', status: '通过', score: '10/10', feedback: 'Web服务正常运行，可访问。' },
                    { item: '防火墙规则配置', status: '失败', score: '5/10', feedback: '部分防火墙规则配置不正确，导致服务不可达。' },
                    { item: 'DNS解析配置', status: '通过', score: '10/10', feedback: 'DNS解析正常。' },
                    { item: '自动化脚本执行', status: '通过', score: '15/15', feedback: '所有自动化脚本均成功执行。' },
                    { item: '系统资源利用率', status: '通过', score: '5/5', feedback: '系统资源利用率在正常范围内。' }
                ],
                'network-config': [
                    { item: 'VLAN配置', status: '通过', score: '10/10', feedback: 'VLAN划分正确，隔离性良好。' },
                    { item: 'OSPF路由协议', status: '失败', score: '0/10', feedback: 'OSPF邻居关系未建立。' },
                    { item: 'ACL访问控制', status: '通过', score: '10/10', feedback: 'ACL规则有效，符合安全策略。' },
                    { item: 'NAT配置', status: '通过', score: '10/10', feedback: 'NAT转换正常，内部网络可访问外部。' },
                    { item: 'VPN隧道', status: '失败', score: '0/10', feedback: 'VPN隧道无法建立。' }
                ],
                'security-hardening': [
                    { item: '操作系统补丁更新', status: '通过', score: '10/10', feedback: '所有操作系统已更新至最新补丁。' },
                    { item: '弱口令检测', status: '失败', score: '0/10', feedback: '发现多个弱口令用户。' },
                    { item: '日志审计配置', status: '通过', score: '10/10', feedback: '日志审计功能已启用并正确配置。' },
                    { item: '入侵检测系统', status: '通过', score: '10/10', feedback: 'IDS系统正常运行，无异常告警。' },
                    { item: '服务端口安全', status: '失败', score: '5/10', feedback: '部分不必要服务端口对外开放。' }
                ]
            };

            scoreButton.addEventListener('click', function() {
                const selectedModule = scoringModuleSelect.value;
                const moduleName = scoringModuleSelect.options[scoringModuleSelect.selectedIndex].text;
                const logs = [
                    `正在启动自动化评分模块: ${moduleName}...`,
                    `正在连接目标环境并收集数据...`,
                    `正在执行各项评估项检查...`,
                    `正在分析配置和运行状态...`,
                    `正在生成详细评分报告...`,
                    `评分过程完成。`
                ];

                terminalArea.innerHTML = `WCM >_ ${logs[0]}<br><br>`;
                resultsTableBody.innerHTML = `<tr><td colspan="4">正在评分，请稍候...</td></tr>`;
                terminalArea.scrollTop = terminalArea.scrollHeight;

                let logIndex = 1;
                const logInterval = setInterval(() => {
                    if (logIndex < logs.length) {
                        terminalArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${logs[logIndex]}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                        logIndex++;
                    } else {
                        clearInterval(logInterval);
                        displayScoringResults(selectedModule);
                        terminalArea.innerHTML += `<br>WCM >_ 评分报告已生成。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1500); // 每1.5秒输出一条日志
            });

            function displayScoringResults(module) {
                const results = scoringData[module];
                resultsTableBody.innerHTML = ''; // 清空现有内容

                results.forEach(item => {
                    const row = resultsTableBody.insertRow();
                    const statusClass = item.status === '通过' ? 'status-pass' : 'status-fail';
                    row.innerHTML = `
                        <td>${item.item}</td>
                        <td class="${statusClass}">${item.status}</td>
                        <td>${item.score}</td>
                        <td>${item.feedback}</td>
                    `;
                });
            }

            // 初始加载时清空表格并显示提示
            resultsTableBody.innerHTML = `<tr><td colspan="4">请选择一个模块并点击“开始自动化评分”按钮。</td></tr>`;
        });
    </script>
</body>
</html>