# WCM Server 部署指南

本文档详细说明如何部署和配置 WCM (VMware Configuration Manager) 服务器。

## 系统要求

### 硬件要求
- CPU: 双核或以上
- 内存: 4GB RAM 或以上
- 存储: 10GB 可用空间
- 网络: 稳定的网络连接

### 软件要求
- 操作系统: Windows 10/11 或 Windows Server 2016+
- Python: 3.8 或以上版本
- VMware Workstation Pro/Player: 最新版本
- 管理员权限

## 快速部署

### 1. 环境准备

```bash
# 克隆或下载项目
cd WCM/server

# 创建虚拟环境（推荐）
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置检查

```bash
# 检查配置
python -c "from config import ServerConfig; print(ServerConfig.validate_config())"
```

### 3. 启动服务器

```bash
# 使用启动脚本（推荐）
python start_server.py

# 或直接启动
python main.py
```

### 4. 验证部署

```bash
# 运行测试脚本
python test_server.py
```

访问 http://127.0.0.1:8080 查看登录页面。

## 详细配置

### 环境变量配置

创建 `.env` 文件：

```env
# 服务器配置
WCM_HOST=0.0.0.0
WCM_PORT=8080
WCM_DEBUG=False

# 安全配置
WCM_SECRET_KEY=your_super_secret_key_here_change_in_production
WCM_TOKEN_EXPIRE=60

# 日志配置
WCM_LOG_LEVEL=INFO
WCM_LOG_FILE=wcm_server.log

# 部署配置
WCM_DEPLOY_TIMEOUT=600
WCM_MAX_PARALLEL=10
```

### 配置文件修改

编辑 `config.py` 文件：

```python
class ServerConfig:
    # 修改默认端口
    PORT = 9000
    
    # 修改默认用户
    # 在生产环境中应该使用数据库
    
    # 添加自定义拓扑模板
    TOPOLOGY_TEMPLATES = {
        # 添加您的自定义模板
    }
```

### 用户管理

默认用户账户：
- 用户名: `admin`
- 密码: `password`

**重要**: 生产环境中必须修改默认密码！

修改用户信息：

```python
# 在 main.py 中修改 USERS_DB
USERS_DB = {
    "admin": {
        "username": "admin",
        "password": "新密码的SHA256哈希值",
        "role": "admin"
    }
}
```

生成密码哈希：

```python
import hashlib
password = "your_new_password"
hashed = hashlib.sha256(password.encode()).hexdigest()
print(hashed)
```

## 生产环境部署

### 1. 使用 Gunicorn (推荐)

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动服务器
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8080
```

### 2. 使用 Windows 服务

创建 Windows 服务脚本 `wcm_service.py`：

```python
import win32serviceutil
import win32service
import win32event
import subprocess
import os

class WCMService(win32serviceutil.ServiceFramework):
    _svc_name_ = "WCMServer"
    _svc_display_name_ = "WCM Server Service"
    _svc_description_ = "VMware Configuration Manager Server"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.process = None

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        if self.process:
            self.process.terminate()
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        os.chdir(r"C:\path\to\WCM\server")
        self.process = subprocess.Popen([
            r"C:\path\to\python.exe", "main.py"
        ])
        win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(WCMService)
```

安装服务：

```bash
python wcm_service.py install
python wcm_service.py start
```

### 3. 使用 Docker (可选)

创建 `Dockerfile`：

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080

CMD ["python", "main.py"]
```

构建和运行：

```bash
docker build -t wcm-server .
docker run -p 8080:8080 wcm-server
```

## 反向代理配置

### Nginx 配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### IIS 配置

安装 URL Rewrite 和 Application Request Routing 模块，然后配置：

```xml
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="WCM Proxy" stopProcessing="true">
          <match url="(.*)" />
          <action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

## 监控和日志

### 日志配置

日志文件位置: `wcm_server.log`

日志级别:
- DEBUG: 详细调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息

### 健康检查

```bash
# 检查服务器状态
curl http://127.0.0.1:8080/api/health

# 预期响应
{
  "status": "healthy",
  "message": "WCM服务器运行正常",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 性能监控

使用以下工具监控服务器性能：
- Windows 性能监视器
- 任务管理器
- 第三方监控工具

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -ano | findstr :8080
   taskkill /PID <PID> /F
   ```

2. **权限问题**
   - 以管理员身份运行
   - 检查文件权限

3. **VMware 检测失败**
   - 确认 VMware Workstation 已安装
   - 检查服务是否运行

4. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt --force-reinstall
   ```

### 调试模式

启用调试模式：

```bash
# 设置环境变量
set WCM_DEBUG=True

# 或修改配置文件
DEBUG = True
```

### 日志分析

查看详细日志：

```bash
# 实时查看日志
tail -f wcm_server.log

# 搜索错误
findstr "ERROR" wcm_server.log
```

## 安全建议

1. **修改默认密码**
2. **使用 HTTPS**
3. **限制访问 IP**
4. **定期更新依赖**
5. **备份配置文件**
6. **监控异常访问**

## 备份和恢复

### 备份

```bash
# 备份配置文件
copy config.py config.py.backup

# 备份 web 文件
xcopy web web_backup /E /I

# 备份日志
copy wcm_server.log logs\wcm_server_%date%.log
```

### 恢复

```bash
# 恢复配置
copy config.py.backup config.py

# 恢复 web 文件
xcopy web_backup web /E /I /Y
```

## 支持

如遇问题，请：
1. 检查日志文件
2. 运行测试脚本
3. 查看系统资源使用情况
4. 确认 VMware 状态
