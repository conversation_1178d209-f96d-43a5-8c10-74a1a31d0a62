"""
VMware Workstation 管理器配置文件
"""
import os

class Config:
    # VMware 默认路径
    VMWARE_DEFAULT_PATHS = [
        r"C:\Program Files (x86)\VMware\VMware Workstation",
        r"C:\Program Files\VMware\VMware Workstation"
    ]
    
    # VMware 配置文件路径
    VMWARE_CONFIG_DIR = os.path.expanduser(r"~\AppData\Roaming\VMware")
    INVENTORY_FILE = os.path.join(VMWARE_CONFIG_DIR, "inventory.vmls")
    PREFERENCES_FILE = os.path.join(VMWARE_CONFIG_DIR, "preferences.ini")
    
    # 默认虚拟机目录
    DEFAULT_VM_PATHS = [
        os.path.expanduser(r"~\Documents\Virtual Machines"),
        r"C:\VMS"
    ]
    
    # 网络配置文件路径
    NETWORKING_FILE = os.path.join(VMWARE_CONFIG_DIR, "networking")
    
    # API 配置
    API_TITLE = "VMware Workstation 管理器"
    API_DESCRIPTION = "用于管理VMware Workstation虚拟机和网络的API"
    API_VERSION = "1.0.0"
    
    # 服务器配置
    HOST = "127.0.0.1"
    PORT = 8000
    DEBUG = True
