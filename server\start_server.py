#!/usr/bin/env python3
"""
WCM Server Startup Script
WCM服务器启动脚本
"""
import os
import sys
import logging
import uvicorn
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent / "client"))

from config import ServerConfig

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, ServerConfig.LOG_LEVEL.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(ServerConfig.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖"""
    try:
        import fastapi
        import uvicorn
        import jwt
        import passlib
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_environment():
    """检查环境"""
    issues = []
    
    # 检查web目录
    if not os.path.exists(ServerConfig.WEB_DIR):
        issues.append(f"Web目录不存在: {ServerConfig.WEB_DIR}")
    
    # 检查HTML文件
    required_files = [
        "index.html", "dashboard.html", "deploy.html", 
        "restore.html", "topology.html", "batch_deploy.html",
        "scoring.html", "troubleshooting.html"
    ]
    
    for file in required_files:
        file_path = os.path.join(ServerConfig.WEB_DIR, file)
        if not os.path.exists(file_path):
            issues.append(f"缺少HTML文件: {file}")
    
    if issues:
        print("环境检查发现问题:")
        for issue in issues:
            print(f"  ✗ {issue}")
        return False
    else:
        print("✓ 环境检查通过")
        return True

def main():
    """主函数"""
    print("=" * 50)
    print("WCM (VMware Configuration Manager) Server")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查环境
    if not check_environment():
        print("\n请确保所有必需的文件都存在后再启动服务器")
        sys.exit(1)
    
    # 验证配置
    config_validation = ServerConfig.validate_config()
    if not config_validation["valid"]:
        print("配置验证失败:")
        for issue in config_validation["issues"]:
            print(f"  ✗ {issue}")
        sys.exit(1)
    
    print(f"\n启动WCM服务器...")
    print(f"主机: {ServerConfig.HOST}")
    print(f"端口: {ServerConfig.PORT}")
    print(f"调试模式: {ServerConfig.DEBUG}")
    print(f"Web目录: {ServerConfig.WEB_DIR}")
    print(f"日志级别: {ServerConfig.LOG_LEVEL}")
    print(f"访问地址: http://{ServerConfig.HOST}:{ServerConfig.PORT}")
    print(f"API文档: http://{ServerConfig.HOST}:{ServerConfig.PORT}/docs")
    print(f"默认用户: admin / password")
    print("-" * 50)
    
    try:
        # 启动服务器
        uvicorn.run(
            "main:app",
            host=ServerConfig.HOST,
            port=ServerConfig.PORT,
            reload=ServerConfig.DEBUG,
            log_level=ServerConfig.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
