"""
WCM Server Configuration
服务器端配置文件
"""
import os
from typing import List, Dict, Any

class ServerConfig:
    # 服务器基本配置
    HOST = os.getenv("WCM_HOST", "127.0.0.1")
    PORT = int(os.getenv("WCM_PORT", "8080"))
    DEBUG = os.getenv("WCM_DEBUG", "True").lower() == "true"
    
    # API配置
    API_TITLE = "WCM - VMware Configuration Manager"
    API_DESCRIPTION = "VMware Workstation管理系统后端API服务"
    API_VERSION = "2.0.0"
    
    # 安全配置
    SECRET_KEY = os.getenv("WCM_SECRET_KEY", "wcm_secret_key_2024_change_in_production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("WCM_TOKEN_EXPIRE", "30"))
    
    # 文件路径配置
    WEB_DIR = os.path.join(os.path.dirname(__file__), "web")
    STATIC_DIR = os.path.join(WEB_DIR, "static")
    TEMPLATES_DIR = os.path.join(WEB_DIR, "templates")
    
    # 日志配置
    LOG_LEVEL = os.getenv("WCM_LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("WCM_LOG_FILE", "wcm_server.log")
    
    # VMware配置 (继承自client配置)
    VMWARE_DEFAULT_PATHS = [
        r"C:\Program Files (x86)\VMware\VMware Workstation",
        r"C:\Program Files\VMware\VMware Workstation"
    ]
    
    VMWARE_CONFIG_DIR = os.path.expanduser(r"~\AppData\Roaming\VMware")
    INVENTORY_FILE = os.path.join(VMWARE_CONFIG_DIR, "inventory.vmls")
    PREFERENCES_FILE = os.path.join(VMWARE_CONFIG_DIR, "preferences.ini")
    NETWORKING_FILE = os.path.join(VMWARE_CONFIG_DIR, "networking")
    
    DEFAULT_VM_PATHS = [
        os.path.expanduser(r"~\Documents\Virtual Machines"),
        r"C:\VMS"
    ]
    
    # 部署配置
    DEPLOYMENT_TIMEOUT = int(os.getenv("WCM_DEPLOY_TIMEOUT", "300"))  # 5分钟
    MAX_PARALLEL_DEPLOYMENTS = int(os.getenv("WCM_MAX_PARALLEL", "5"))
    
    # 评分配置
    SCORING_CRITERIA = {
        "memory": {
            "weight": 30,
            "thresholds": {
                "excellent": 2048,  # MB
                "good": 1024,
                "basic": 512
            }
        },
        "cpu": {
            "weight": 20,
            "thresholds": {
                "excellent": 4,  # vCPUs
                "good": 2,
                "basic": 1
            }
        },
        "network": {
            "weight": 30,
            "thresholds": {
                "excellent": 2,  # adapters
                "good": 1,
                "basic": 0
            }
        },
        "naming": {
            "weight": 20,
            "min_length": 3
        }
    }
    
    # 故障排除配置
    TROUBLESHOOT_TIMEOUT = int(os.getenv("WCM_TROUBLESHOOT_TIMEOUT", "60"))  # 1分钟
    
    # 网络拓扑模板
    TOPOLOGY_TEMPLATES = {
        "simple_lan": {
            "name": "简单局域网",
            "description": "基本的局域网拓扑，包含一个交换机和多个主机",
            "nodes": 5,
            "complexity": "简单",
            "networks": ["LAN1"],
            "vms": [
                {"name": "PC1", "type": "client", "network": "LAN1"},
                {"name": "PC2", "type": "client", "network": "LAN1"},
                {"name": "PC3", "type": "client", "network": "LAN1"},
                {"name": "Server1", "type": "server", "network": "LAN1"},
                {"name": "Switch1", "type": "switch", "network": "LAN1"}
            ]
        },
        "routed_network": {
            "name": "路由网络",
            "description": "包含路由器的多子网网络拓扑",
            "nodes": 8,
            "complexity": "中等",
            "networks": ["LAN1", "LAN2", "WAN"],
            "vms": [
                {"name": "PC1", "type": "client", "network": "LAN1"},
                {"name": "PC2", "type": "client", "network": "LAN1"},
                {"name": "PC3", "type": "client", "network": "LAN2"},
                {"name": "PC4", "type": "client", "network": "LAN2"},
                {"name": "Server1", "type": "server", "network": "LAN1"},
                {"name": "Server2", "type": "server", "network": "LAN2"},
                {"name": "Router1", "type": "router", "networks": ["LAN1", "LAN2", "WAN"]},
                {"name": "Gateway", "type": "gateway", "network": "WAN"}
            ]
        },
        "enterprise_network": {
            "name": "企业网络",
            "description": "复杂的企业级网络拓扑，包含多层交换和安全设备",
            "nodes": 15,
            "complexity": "复杂",
            "networks": ["DMZ", "LAN1", "LAN2", "LAN3", "WAN"],
            "vms": [
                {"name": "PC1", "type": "client", "network": "LAN1"},
                {"name": "PC2", "type": "client", "network": "LAN1"},
                {"name": "PC3", "type": "client", "network": "LAN2"},
                {"name": "PC4", "type": "client", "network": "LAN2"},
                {"name": "PC5", "type": "client", "network": "LAN3"},
                {"name": "PC6", "type": "client", "network": "LAN3"},
                {"name": "WebServer", "type": "server", "network": "DMZ"},
                {"name": "DBServer", "type": "server", "network": "LAN1"},
                {"name": "FileServer", "type": "server", "network": "LAN2"},
                {"name": "CoreSwitch", "type": "switch", "networks": ["LAN1", "LAN2", "LAN3"]},
                {"name": "AccessSwitch1", "type": "switch", "network": "LAN1"},
                {"name": "AccessSwitch2", "type": "switch", "network": "LAN2"},
                {"name": "Firewall", "type": "firewall", "networks": ["DMZ", "LAN1", "WAN"]},
                {"name": "Router", "type": "router", "networks": ["WAN", "DMZ"]},
                {"name": "Gateway", "type": "gateway", "network": "WAN"}
            ]
        }
    }
    
    # 用户角色配置
    USER_ROLES = {
        "admin": {
            "permissions": ["read", "write", "deploy", "manage", "troubleshoot"],
            "description": "系统管理员，拥有所有权限"
        },
        "operator": {
            "permissions": ["read", "deploy", "troubleshoot"],
            "description": "操作员，可以部署和故障排除"
        },
        "viewer": {
            "permissions": ["read"],
            "description": "查看者，只能查看信息"
        }
    }
    
    @classmethod
    def get_web_path(cls, filename: str) -> str:
        """获取web文件的完整路径"""
        return os.path.join(cls.WEB_DIR, filename)
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        
        # 检查web目录
        if not os.path.exists(cls.WEB_DIR):
            issues.append(f"Web目录不存在: {cls.WEB_DIR}")
        
        # 检查VMware配置目录
        if not os.path.exists(cls.VMWARE_CONFIG_DIR):
            issues.append(f"VMware配置目录不存在: {cls.VMWARE_CONFIG_DIR}")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues
        }
