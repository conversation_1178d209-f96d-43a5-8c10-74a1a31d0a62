#!/usr/bin/env python3
"""
测试多网卡功能和pvnID设置
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def test_vm_multi_nic():
    """测试虚拟机多网卡配置"""
    print("=== 测试虚拟机多网卡配置 ===")
    
    # 首先获取可用的LAN网络
    try:
        response = requests.get(f"{BASE_URL}/api/networks")
        if response.status_code == 200:
            networks = response.json()
            print(f"可用的LAN网络: {len(networks)}个")
            for net in networks:
                print(f"  - {net['name']}: {net['pvn_id']}")
        else:
            print("获取网络列表失败")
            return False
    except Exception as e:
        print(f"获取网络列表错误: {e}")
        return False
    
    if not networks:
        print("没有可用的LAN网络")
        return False
    
    # 配置多网卡
    vm_id = "1"  # 测试虚拟机1
    update_data = {
        "display_name": "多网卡测试虚拟机",
        "network_adapters": [
            {
                "index": 0,
                "connection_type": "nat",
                "address_type": "generated"
            },
            {
                "index": 1,
                "connection_type": "pvn",
                "vnet": networks[0]['name'],  # 使用第一个LAN网络
                "address_type": "generated"
            }
        ]
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/api/vms/{vm_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"更新虚拟机配置: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"更新成功: {result}")
            
            # 验证配置是否正确应用
            verify_response = requests.get(f"{BASE_URL}/api/vms/{vm_id}")
            if verify_response.status_code == 200:
                vm_info = verify_response.json()
                adapters = vm_info.get('network_adapters', [])
                print(f"验证结果: 找到 {len(adapters)} 个网络适配器")
                
                for adapter in adapters:
                    print(f"  适配器 {adapter['index']}: {adapter['connection_type']}")
                    if adapter['connection_type'] == 'pvn':
                        print(f"    LAN网络: {adapter.get('vnet', 'N/A')}")
                        print(f"    PVN ID: {adapter.get('pvn_id', 'N/A')}")
                
                return True
            else:
                print("验证配置失败")
                return False
        else:
            print(f"更新失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"更新虚拟机配置错误: {e}")
        return False

def test_add_network_adapter():
    """测试添加网络适配器"""
    print("\n=== 测试添加网络适配器 ===")
    
    vm_id = "2"  # 测试虚拟机2
    adapter_data = {
        "connection_type": "bridged",
        "address_type": "generated"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/vms/{vm_id}/network-adapters",
            json=adapter_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"添加网络适配器: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"添加成功: {result}")
            return True
        else:
            print(f"添加失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"添加网络适配器错误: {e}")
        return False

def test_remove_network_adapter():
    """测试删除网络适配器"""
    print("\n=== 测试删除网络适配器 ===")
    
    vm_id = "2"  # 测试虚拟机2
    adapter_index = 1  # 删除适配器1
    
    try:
        response = requests.delete(f"{BASE_URL}/api/vms/{vm_id}/network-adapters/{adapter_index}")
        
        print(f"删除网络适配器: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"删除成功: {result}")
            return True
        else:
            print(f"删除失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"删除网络适配器错误: {e}")
        return False

def main():
    """运行所有测试"""
    print("=== VMware多网卡功能测试 ===\n")
    
    tests = [
        ("虚拟机多网卡配置", test_vm_multi_nic),
        ("添加网络适配器", test_add_network_adapter),
        ("删除网络适配器", test_remove_network_adapter),
    ]
    
    results = []
    for name, test_func in tests:
        result = test_func()
        results.append((name, result))
    
    print("\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✓" if result else "✗"
        print(f"{status} {name}")
    
    print(f"\n通过: {passed}/{total}")

if __name__ == "__main__":
    main()
