<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMware Workstation 管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
        }

        .header h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 0.875rem;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            gap: 4px;
        }

        .nav-tab {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #64748b;
            transition: all 0.2s;
        }

        .nav-tab.active {
            background: #3b82f6;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            background: #f1f5f9;
            color: #1a202c;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 24px;
        }

        .card h3 {
            color: #1a202c;
            margin-bottom: 16px;
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .grid {
            display: grid;
            gap: 24px;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .network-adapter {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .network-adapter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .network-adapter-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .network-adapter-title::before {
            content: "🌐";
            font-size: 1rem;
        }

        .adapter-controls {
            display: flex;
            gap: 8px;
        }

        /* 大模态框样式 */
        .modal-content.large {
            max-width: 800px;
            width: 95%;
        }

        .network-adapters-container {
            max-height: 450px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            background: #f8fafc;
        }

        .network-adapters-container::-webkit-scrollbar {
            width: 6px;
        }

        .network-adapters-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .network-adapters-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .network-adapters-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
            font-style: italic;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: 500;
            color: #64748b;
            font-size: 0.875rem;
        }

        .status-value {
            font-weight: 600;
            color: #1a202c;
            font-size: 0.875rem;
        }

        .status-success {
            color: #059669;
        }

        .status-error {
            color: #dc2626;
        }

        .status-warning {
            color: #d97706;
        }

        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover {
            background: #2563eb;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.75rem;
        }

        .btn-danger {
            background: #dc2626;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-success {
            background: #059669;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-secondary {
            background: #64748b;
        }

        .btn-secondary:hover {
            background: #475569;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .list-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .list-item {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:hover {
            background: #f9fafb;
        }

        .list-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .list-item-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.875rem;
        }

        .list-item-meta {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 2px 0;
        }

        .list-item-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-success {
            background: #dcfce7;
            color: #166534;
        }

        .badge-error {
            background: #fef2f2;
            color: #991b1b;
        }

        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 0.875rem;
        }

        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            box-shadow: 0 25px 50px rgba(0,0,0,0.25);
            animation: modalSlideIn 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
            max-height: calc(90vh - 120px);
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            flex-shrink: 0;
        }

        .close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close:hover {
            color: #374151;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 16px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>VMware Workstation 管理器</h1>
            <p>现代化的虚拟机和网络管理工具</p>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">概览</button>
            <button class="nav-tab" onclick="showTab('vms')">虚拟机</button>
            <button class="nav-tab" onclick="showTab('networks')">网络</button>
        </div>

        <!-- 概览标签页 -->
        <div id="overview-tab" class="tab-content active">
            <div class="grid grid-2">
                <div class="card">
                    <h3>📊 系统状态</h3>
                    <div id="system-info">
                        <div class="loading">正在加载系统信息...</div>
                    </div>
                    <button class="btn btn-sm" onclick="refreshSystemInfo()">刷新</button>
                </div>

                <div class="card">
                    <h3>📈 统计信息</h3>
                    <div id="stats-info">
                        <div class="loading">正在加载统计信息...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 虚拟机标签页 -->
        <div id="vms-tab" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3>🖥️ 虚拟机管理</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-sm" onclick="refreshVMs()">刷新</button>
                    </div>
                </div>
                <div id="vm-list">
                    <div class="loading">正在加载虚拟机列表...</div>
                </div>
            </div>
        </div>

        <!-- 网络标签页 -->
        <div id="networks-tab" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3>🌐 网络管理</h3>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-success btn-sm" onclick="showCreateNetworkModal()">创建网络</button>
                        <button class="btn btn-sm" onclick="refreshNetworks()">刷新</button>
                    </div>
                </div>
                <div id="network-list">
                    <div class="loading">正在加载网络列表...</div>
                </div>
            </div>
        </div>


    </div>

    <!-- 创建网络模态框 -->
    <div id="createNetworkModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">创建新的LAN网络</h3>
                <button class="close" onclick="closeModal('createNetworkModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label for="networkName">网络名称</label>
                    <input type="text" id="networkName" placeholder="输入网络名称">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('createNetworkModal')">取消</button>
                <button class="btn btn-success" onclick="createNetwork()">创建</button>
            </div>
        </div>
    </div>



    <!-- 编辑虚拟机模态框 -->
    <div id="editVmModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 class="modal-title">编辑虚拟机</h3>
                <button class="close" onclick="closeModal('editVmModal')">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 基本配置 -->
                <h4 style="margin-bottom: 16px; color: #374151; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;">基本配置</h4>
                <div class="grid grid-2" style="margin-bottom: 24px;">
                    <div class="input-group">
                        <label for="vmDisplayName">显示名称</label>
                        <input type="text" id="vmDisplayName" placeholder="虚拟机显示名称">
                    </div>
                    <div class="input-group">
                        <label for="vmMemorySize">内存大小 (MB)</label>
                        <input type="number" id="vmMemorySize" placeholder="2048">
                    </div>
                    <div class="input-group">
                        <label for="vmCpuCount">CPU核心数</label>
                        <input type="number" id="vmCpuCount" placeholder="2">
                    </div>
                </div>

                <!-- 网络配置 -->
                <h4 style="margin-bottom: 16px; color: #374151; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px;">网络配置</h4>
                <div class="network-adapters-container">
                    <div id="networkAdaptersContainer">
                        <!-- 网络适配器列表将在这里动态生成 -->
                    </div>
                    <div style="margin-top: 16px; text-align: center;">
                        <button type="button" class="btn btn-success btn-sm" onclick="addNetworkAdapter()">➕ 添加网卡</button>
                    </div>
                </div>

                <input type="hidden" id="editVmId">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editVmModal')">取消</button>
                <button class="btn btn-success" onclick="saveVmChanges()">保存</button>
            </div>
        </div>
    </div>



    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshSystemInfo();
            refreshVMs();
            refreshNetworks();
        });

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签的active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活对应的导航标签
            event.target.classList.add('active');

            // 根据标签页刷新数据
            switch(tabName) {
                case 'overview':
                    refreshSystemInfo();
                    break;
                case 'vms':
                    refreshVMs();
                    break;
                case 'networks':
                    refreshNetworks();
                    break;
            }
        }

        // 通用模态框控制
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        // 刷新系统信息
        async function refreshSystemInfo() {
            const container = document.getElementById('system-info');
            container.innerHTML = '<div class="loading">正在加载系统信息...</div>';
            
            try {
                const response = await fetch('/api/system/info');
                const data = await response.json();
                
                if (response.ok) {
                    displaySystemInfo(data);
                } else {
                    container.innerHTML = `<div class="error">加载失败: ${data.detail}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示系统信息
        function displaySystemInfo(data) {
            const container = document.getElementById('system-info');
            const vmware = data.vmware_installation;
            const status = data.system_status;

            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">VMware状态</span>
                    <span class="badge ${vmware.installed ? 'badge-success' : 'badge-error'}">
                        ${vmware.installed ? '已安装' : '未安装'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">版本</span>
                    <span class="status-value">${vmware.version || '未知'}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">运行状态</span>
                    <span class="badge ${data.vmware_running ? 'badge-success' : 'badge-warning'}">
                        ${data.vmware_running ? '运行中' : '未运行'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">安装路径</span>
                    <span class="status-value" style="font-size: 0.75rem;">${vmware.install_path || '未找到'}</span>
                </div>
            `;

            // 更新统计信息
            const statsContainer = document.getElementById('stats-info');
            if (statsContainer) {
                statsContainer.innerHTML = `
                    <div class="status-item">
                        <span class="status-label">虚拟机总数</span>
                        <span class="status-value">${status.total_vms}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">文件夹数量</span>
                        <span class="status-value">${status.total_folders}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">LAN网络数量</span>
                        <span class="status-value">${status.lan_networks}</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">配置文件状态</span>
                        <span class="badge ${status.config_files_exist.inventory && status.config_files_exist.preferences ? 'badge-success' : 'badge-error'}">
                            ${status.config_files_exist.inventory && status.config_files_exist.preferences ? '正常' : '异常'}
                        </span>
                    </div>
                `;
            }
        }

        // 刷新虚拟机列表
        async function refreshVMs() {
            const container = document.getElementById('vm-list');
            container.innerHTML = '<div class="loading">正在加载虚拟机信息...</div>';

            try {
                const response = await fetch('/api/vms');
                const data = await response.json();

                if (response.ok) {
                    displayVMs(data);
                } else {
                    container.innerHTML = `<div class="alert alert-error">加载失败: ${data.detail}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="alert alert-error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示虚拟机列表
        function displayVMs(data) {
            const container = document.getElementById('vm-list');
            const vms = data.virtual_machines;

            if (vms.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无虚拟机</div>';
                return;
            }

            let html = '<div class="list-container">';
            vms.forEach(vm => {
                const statusBadge = vm.exists ? 'badge-success' : 'badge-error';
                const statusText = vm.exists ? '正常' : '文件缺失';

                html += `
                    <div class="list-item">
                        <div class="list-item-header">
                            <div class="list-item-title">${vm.display_name}</div>
                            <span class="badge ${statusBadge}">${statusText}</span>
                        </div>
                        <div class="list-item-meta">ID: ${vm.id} | UUID: ${vm.uuid}</div>
                        ${vm.guest_os ? `<div class="list-item-meta">系统: ${vm.guest_os}</div>` : ''}
                        ${vm.memory_size ? `<div class="list-item-meta">内存: ${vm.memory_size}MB | CPU: ${vm.num_vcpus || 1}核</div>` : ''}
                        ${vm.ethernet_connection_type ? `<div class="list-item-meta">网络: ${vm.ethernet_connection_type}</div>` : ''}
                        <div class="list-item-meta">路径: ${vm.config_path}</div>
                        <div class="list-item-actions">
                            <button class="btn btn-sm" onclick="editVm('${vm.id}')">编辑</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteVm('${vm.id}', '${vm.display_name}')">删除</button>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            container.innerHTML = html;
        }

        // 刷新网络列表
        async function refreshNetworks() {
            const container = document.getElementById('network-list');
            container.innerHTML = '<div class="loading">正在加载网络信息...</div>';

            try {
                const response = await fetch('/api/networks');
                const data = await response.json();

                if (response.ok) {
                    displayNetworks(data);
                } else {
                    container.innerHTML = `<div class="alert alert-error">加载失败: ${data.detail}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="alert alert-error">网络错误: ${error.message}</div>`;
            }
        }

        // 显示网络列表
        function displayNetworks(networks) {
            const container = document.getElementById('network-list');

            if (networks.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无LAN网络</div>';
                return;
            }

            let html = '<div class="list-container">';
            networks.forEach(network => {
                html += `
                    <div class="list-item">
                        <div class="list-item-header">
                            <div class="list-item-title">${network.name}</div>
                            <span class="badge badge-success">活跃</span>
                        </div>
                        <div class="list-item-meta">索引: ${network.index}</div>
                        <div class="list-item-meta">PVN ID: ${network.pvn_id}</div>
                        <div class="list-item-actions">
                            <button class="btn btn-sm btn-danger" onclick="deleteNetwork('${network.name}')">删除</button>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            container.innerHTML = html;
        }

        // 显示创建网络模态框
        function showCreateNetworkModal() {
            showModal('createNetworkModal');
            document.getElementById('networkName').value = '';
        }



        // 全局变量存储当前编辑的虚拟机网络适配器
        let currentNetworkAdapters = [];

        // 虚拟机管理功能
        async function editVm(vmId) {
            try {
                const response = await fetch(`/api/vms/${vmId}`);
                const vm = await response.json();

                if (response.ok) {
                    document.getElementById('editVmId').value = vmId;
                    document.getElementById('vmDisplayName').value = vm.display_name || '';
                    document.getElementById('vmMemorySize').value = vm.memory_size || '';
                    document.getElementById('vmCpuCount').value = vm.num_vcpus || '';

                    // 加载网络适配器
                    currentNetworkAdapters = vm.network_adapters || [];
                    if (currentNetworkAdapters.length === 0) {
                        // 如果没有网络适配器，创建一个默认的
                        currentNetworkAdapters = [{
                            index: 0,
                            connection_type: vm.ethernet_connection_type || 'nat',
                            vnet: vm.ethernet_vnet || '',
                            address_type: 'generated',
                            generated_address: '',
                            wake_on_lan: false,
                            link_state_propagation: false
                        }];
                    }

                    await renderNetworkAdapters();
                    showModal('editVmModal');
                } else {
                    alert(`获取虚拟机信息失败: ${vm.detail}`);
                }
            } catch (error) {
                alert(`网络错误: ${error.message}`);
            }
        }

        // 渲染网络适配器列表
        async function renderNetworkAdapters() {
            const container = document.getElementById('networkAdaptersContainer');

            if (currentNetworkAdapters.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无网络适配器</div>';
                return;
            }

            // 加载LAN网络列表
            const lanNetworks = await getLanNetworksList();

            let html = '';
            currentNetworkAdapters.forEach((adapter, index) => {
                html += `
                    <div class="network-adapter" data-index="${adapter.index}">
                        <div class="network-adapter-header">
                            <div class="network-adapter-title">网络适配器 ${adapter.index}</div>
                            <div class="adapter-controls">
                                ${currentNetworkAdapters.length > 1 ?
                                    `<button type="button" class="btn btn-danger btn-sm" onclick="removeNetworkAdapter(${adapter.index})">删除</button>` :
                                    ''
                                }
                            </div>
                        </div>
                        <div class="grid grid-2">
                            <div class="input-group">
                                <label>网络类型</label>
                                <select onchange="updateAdapterConfig(${adapter.index}, 'connection_type', this.value)">
                                    <option value="nat" ${adapter.connection_type === 'nat' ? 'selected' : ''}>NAT</option>
                                    <option value="bridged" ${adapter.connection_type === 'bridged' ? 'selected' : ''}>桥接</option>
                                    <option value="hostonly" ${adapter.connection_type === 'hostonly' ? 'selected' : ''}>仅主机</option>
                                    <option value="pvn" ${adapter.connection_type === 'pvn' ? 'selected' : ''}>LAN网络</option>
                                </select>
                            </div>
                            ${adapter.connection_type === 'pvn' ? `
                                <div class="input-group">
                                    <label>LAN网络</label>
                                    <select onchange="updateAdapterConfigWithPvnId(${adapter.index}, this.value)">
                                        <option value="">请选择LAN网络</option>
                                        ${lanNetworks.map(net =>
                                            `<option value="${net.name}" data-pvn-id="${net.pvn_id}" ${adapter.vnet === net.name ? 'selected' : ''}>${net.name}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                            ` : ''}
                        </div>
                        ${adapter.connection_type === 'pvn' && adapter.pvn_id ? `
                            <div style="margin-top: 8px; padding: 8px; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 4px;">
                                <div style="font-size: 0.75rem; color: #0369a1; font-weight: 500; margin-bottom: 4px;">PVN ID:</div>
                                <div style="font-family: monospace; font-size: 0.7rem; color: #0c4a6e; word-break: break-all;">${adapter.pvn_id}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 获取LAN网络列表
        async function getLanNetworksList() {
            try {
                const response = await fetch('/api/networks');
                if (response.ok) {
                    return await response.json();
                }
            } catch (error) {
                console.error('获取LAN网络列表失败:', error);
            }
            return [];
        }

        // 更新适配器配置
        function updateAdapterConfig(adapterIndex, field, value) {
            const adapter = currentNetworkAdapters.find(a => a.index === adapterIndex);
            if (adapter) {
                adapter[field] = value;
                // 如果改变了网络类型，重新渲染
                if (field === 'connection_type') {
                    renderNetworkAdapters();
                }
            }
        }

        // 更新适配器配置并设置pvnID
        async function updateAdapterConfigWithPvnId(adapterIndex, networkName) {
            const adapter = currentNetworkAdapters.find(a => a.index === adapterIndex);
            if (adapter) {
                adapter.vnet = networkName;

                // 如果选择了LAN网络，获取对应的pvnID
                if (networkName) {
                    try {
                        const response = await fetch('/api/networks');
                        if (response.ok) {
                            const networks = await response.json();
                            const selectedNetwork = networks.find(net => net.name === networkName);
                            if (selectedNetwork && selectedNetwork.pvn_id) {
                                adapter.pvn_id = selectedNetwork.pvn_id;
                            }
                        }
                    } catch (error) {
                        console.error('获取网络pvnID失败:', error);
                    }
                } else {
                    // 清除pvnID
                    adapter.pvn_id = '';
                }

                // 重新渲染以显示pvnID
                renderNetworkAdapters();
            }
        }

        // 添加网络适配器
        function addNetworkAdapter() {
            // 找到下一个可用的索引
            const usedIndices = currentNetworkAdapters.map(a => a.index);
            let nextIndex = 0;
            while (usedIndices.includes(nextIndex) && nextIndex < 10) {
                nextIndex++;
            }

            if (nextIndex >= 10) {
                alert('最多支持10个网络适配器');
                return;
            }

            const newAdapter = {
                index: nextIndex,
                connection_type: 'nat',
                vnet: '',
                address_type: 'generated',
                generated_address: '',
                wake_on_lan: false,
                link_state_propagation: false
            };

            currentNetworkAdapters.push(newAdapter);
            renderNetworkAdapters();
        }

        // 删除网络适配器
        function removeNetworkAdapter(adapterIndex) {
            if (currentNetworkAdapters.length <= 1) {
                alert('至少需要保留一个网络适配器');
                return;
            }

            if (confirm(`确定要删除网络适配器 ${adapterIndex} 吗？`)) {
                currentNetworkAdapters = currentNetworkAdapters.filter(a => a.index !== adapterIndex);
                renderNetworkAdapters();
            }
        }

        async function saveVmChanges() {
            const vmId = document.getElementById('editVmId').value;

            const vmData = {
                display_name: document.getElementById('vmDisplayName').value,
                memory_size: document.getElementById('vmMemorySize').value,
                num_vcpus: document.getElementById('vmCpuCount').value,
                network_adapters: currentNetworkAdapters
            };

            try {
                const response = await fetch(`/api/vms/${vmId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(vmData)
                });

                const data = await response.json();

                if (response.ok) {
                    alert('虚拟机配置更新成功！');
                    closeModal('editVmModal');
                    refreshVMs();
                } else {
                    alert(`更新失败: ${data.detail}`);
                }
            } catch (error) {
                alert(`网络错误: ${error.message}`);
            }
        }

        async function deleteVm(vmId, vmName) {
            if (!confirm(`确定要删除虚拟机 "${vmName}" 吗？此操作不可撤销！`)) {
                return;
            }

            try {
                const response = await fetch(`/api/vms/${vmId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (response.ok) {
                    alert('虚拟机删除成功！');
                    refreshVMs();
                } else {
                    alert(`删除失败: ${data.detail}`);
                }
            } catch (error) {
                alert(`网络错误: ${error.message}`);
            }
        }



        // 创建网络
        async function createNetwork() {
            const networkName = document.getElementById('networkName').value.trim();

            if (!networkName) {
                alert('请输入网络名称');
                return;
            }

            try {
                const response = await fetch('/api/networks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name: networkName })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('网络创建成功！');
                    closeModal('createNetworkModal');
                    refreshNetworks();
                } else {
                    alert(`创建失败: ${data.detail}`);
                }
            } catch (error) {
                alert(`网络错误: ${error.message}`);
            }
        }



        // 删除网络
        async function deleteNetwork(networkName) {
            if (!confirm(`确定要删除网络 "${networkName}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/api/networks/${encodeURIComponent(networkName)}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (response.ok) {
                    alert('网络删除成功！');
                    refreshNetworks();
                } else {
                    alert(`删除失败: ${data.detail}`);
                }
            } catch (error) {
                alert(`网络错误: ${error.message}`);
            }
        }



        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = ['createNetworkModal', 'editVmModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (event.target == modal) {
                    closeModal(modalId);
                }
            });
        }
    </script>
</body>
</html>
