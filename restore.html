<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 环境恢复</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel label {
            font-size: 1.1em;
            color: #333;
        }

        .control-panel select {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            border-radius: 4px;
            outline: none;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .control-panel select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .restore-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        .restore-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .restore-button:active {
            transform: translateY(0);
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 400px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <label for="environment-select">选择要恢复的环境:</label>
            <select id="environment-select">
                <option value="nsm-infra-oam">网络系统管理 - 基础设施编程与自动化运维</option>
                <option value="worldskills-net">世界技能大赛 - 网络</option>
                <option value="national-net">全国网络大赛</option>
                <option value="provincial-net">省级网络大赛</option>
                <option value="municipal-net">市级网络大赛</option>
                <!-- 更多预定义拓扑 -->
            </select>
            <button class="restore-button">一键恢复</button>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待恢复指令...
            <br>
            <br>
            [2025-07-11 23:45:00] 系统启动，准备就绪。
            <br>
            [2025-07-11 23:45:05] 检测到可恢复环境：网络系统管理 - 基础设施编程与自动化运维。
            <br>
            [2025-07-11 23:45:10] 请选择一个环境并点击“一键恢复”按钮。
        </div>

        <!-- 移除 SVG 装饰 -->
        <!-- <svg class="svg-decoration svg-restore" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path d="M100 10 L190 70 L160 190 L40 190 L10 70 Z" stroke="#ff00ff" stroke-width="2" fill="none" />
            <circle cx="100" cy="100" r="40" stroke="#00ffcc" stroke-width="2" fill="none" />
            <line x1="100" y1="60" x2="100" y2="140" stroke="#00ccff" stroke-width="1" />
            <line x1="60" y1="100" x2="140" y2="100" stroke="#00ccff" stroke-width="1" />
            <path d="M100 100 L120 80 L100 60 L80 80 Z" fill="#00ffcc" />
        </svg> -->
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const restoreButton = document.querySelector('.restore-button');
            const terminalArea = document.querySelector('.terminal-area');
            const environmentSelect = document.getElementById('environment-select');

            const logMessages = [
                "正在连接目标环境...",
                "正在检查环境状态...",
                "正在创建恢复点 (如果需要)...",
                "正在回滚虚拟机状态到初始快照...",
                "正在验证恢复操作...",
                "环境已成功恢复到初始状态！",
                "所有评分先决条件已满足。"
            ];

            restoreButton.addEventListener('click', function() {
                const selectedEnv = environmentSelect.options[environmentSelect.selectedIndex].text;
                terminalArea.innerHTML = `WCM >_ 正在恢复环境: ${selectedEnv}<br><br>`;
                let i = 0;
                const interval = setInterval(() => {
                    if (i < logMessages.length) {
                        terminalArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${logMessages[i]}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight; // 滚动到底部
                        i++;
                    } else {
                        clearInterval(interval);
                        terminalArea.innerHTML += `<br>WCM >_ 恢复过程已完成。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1000); // 每秒输出一条日志
            });
        });
    </script>
</body>
</html>