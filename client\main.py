"""
VMware Workstation 管理器 - FastAPI 主应用
"""
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import os

from config import Config
from vmware_detector import VMwareDetector
from vmware_config_parser import VMwareConfigParser
from vmware_network_manager import VMwareNetworkManager

# 创建FastAPI应用
app = FastAPI(
    title=Config.API_TITLE,
    description=Config.API_DESCRIPTION,
    version=Config.API_VERSION
)

# 初始化组件
vmware_detector = VMwareDetector()
config_parser = VMwareConfigParser()
network_manager = VMwareNetworkManager()

# 创建模板目录
os.makedirs("templates", exist_ok=True)

# 设置模板
templates = Jinja2Templates(directory="templates")

# Pydantic 模型
class NetworkCreate(BaseModel):
    name: str

class NetworkRename(BaseModel):
    old_name: str
    new_name: str

class NetworkAdapter(BaseModel):
    index: Optional[int] = None
    connection_type: str = "nat"
    vnet: Optional[str] = None
    address_type: Optional[str] = "generated"
    generated_address: Optional[str] = None
    wake_on_lan: Optional[bool] = False
    link_state_propagation: Optional[bool] = False

class VmUpdate(BaseModel):
    display_name: Optional[str] = None
    memory_size: Optional[str] = None
    num_vcpus: Optional[str] = None
    network_type: Optional[str] = None
    lan_network: Optional[str] = None
    network_adapters: Optional[List[NetworkAdapter]] = None

# API 路由

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/api/system/info")
async def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    try:
        vmware_info = vmware_detector.get_vmware_info()
        system_status = config_parser.get_system_status()
        
        return {
            "vmware_installation": vmware_info,
            "system_status": system_status,
            "vmware_running": vmware_detector.is_vmware_running(),
            "default_vm_directories": vmware_detector.get_default_vm_directories()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")

@app.get("/api/vms")
async def get_virtual_machines() -> Dict[str, Any]:
    """获取虚拟机列表"""
    try:
        inventory = config_parser.parse_inventory()
        return inventory
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取虚拟机列表失败: {str(e)}")

@app.get("/api/vms/{vm_id}")
async def get_virtual_machine(vm_id: str) -> Dict[str, Any]:
    """获取指定虚拟机信息"""
    try:
        vm_info = config_parser.get_vm_by_id(vm_id)
        if not vm_info:
            raise HTTPException(status_code=404, detail=f"虚拟机 ID {vm_id} 不存在")
        return vm_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取虚拟机信息失败: {str(e)}")

@app.get("/api/vms/name/{vm_name}")
async def get_virtual_machine_by_name(vm_name: str) -> Dict[str, Any]:
    """根据名称获取虚拟机信息"""
    try:
        vm_info = config_parser.get_vm_by_name(vm_name)
        if not vm_info:
            raise HTTPException(status_code=404, detail=f"虚拟机 '{vm_name}' 不存在")
        return vm_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取虚拟机信息失败: {str(e)}")

@app.put("/api/vms/{vm_id}")
async def update_virtual_machine(vm_id: str, vm_update: VmUpdate) -> Dict[str, Any]:
    """更新虚拟机配置"""
    try:
        # 构建更新数据
        update_data = {}
        if vm_update.display_name is not None:
            update_data['display_name'] = vm_update.display_name
        if vm_update.memory_size is not None:
            update_data['memory_size'] = vm_update.memory_size
        if vm_update.num_vcpus is not None:
            update_data['num_vcpus'] = vm_update.num_vcpus
        if vm_update.network_type is not None:
            update_data['network_type'] = vm_update.network_type
        if vm_update.lan_network is not None:
            update_data['lan_network'] = vm_update.lan_network
        if vm_update.network_adapters is not None:
            update_data['network_adapters'] = [adapter.model_dump() for adapter in vm_update.network_adapters]

        if not update_data:
            raise HTTPException(status_code=400, detail="没有提供要更新的数据")

        success = config_parser.update_vm_config(vm_id, update_data)
        if not success:
            raise HTTPException(status_code=404, detail=f"虚拟机 ID {vm_id} 不存在或更新失败")

        return {"success": True, "message": "虚拟机配置更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新虚拟机配置失败: {str(e)}")

@app.delete("/api/vms/{vm_id}")
async def delete_virtual_machine(vm_id: str) -> Dict[str, Any]:
    """删除虚拟机"""
    try:
        success = config_parser.delete_vm_from_inventory(vm_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"虚拟机 ID {vm_id} 不存在或删除失败")

        return {"success": True, "message": "虚拟机删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除虚拟机失败: {str(e)}")



@app.get("/api/networks")
async def get_lan_networks() -> List[Dict[str, Any]]:
    """获取LAN网络列表"""
    try:
        return network_manager.get_lan_networks()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取网络列表失败: {str(e)}")

@app.post("/api/networks")
async def create_lan_network(network: NetworkCreate) -> Dict[str, Any]:
    """创建LAN网络"""
    try:
        result = network_manager.create_lan_network(network.name)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建网络失败: {str(e)}")

@app.delete("/api/networks/{network_name}")
async def delete_lan_network(network_name: str) -> Dict[str, Any]:
    """删除LAN网络"""
    try:
        result = network_manager.delete_lan_network(network_name)
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除网络失败: {str(e)}")

@app.put("/api/networks/rename")
async def rename_lan_network(network_rename: NetworkRename) -> Dict[str, Any]:
    """重命名LAN网络"""
    try:
        result = network_manager.rename_lan_network(
            network_rename.old_name, 
            network_rename.new_name
        )
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重命名网络失败: {str(e)}")

@app.get("/api/config/inventory")
async def get_inventory_config() -> Dict[str, Any]:
    """获取inventory配置"""
    try:
        return config_parser.parse_inventory()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取inventory配置失败: {str(e)}")

@app.get("/api/config/preferences")
async def get_preferences_config() -> Dict[str, Any]:
    """获取preferences配置"""
    try:
        return config_parser.parse_preferences()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取preferences配置失败: {str(e)}")

@app.post("/api/vms/{vm_id}/network-adapters")
async def add_network_adapter(vm_id: str, adapter: NetworkAdapter) -> Dict[str, Any]:
    """添加网络适配器"""
    try:
        success = config_parser.add_network_adapter(vm_id, adapter.model_dump())
        if not success:
            raise HTTPException(status_code=400, detail="添加网络适配器失败")

        return {"success": True, "message": "网络适配器添加成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加网络适配器失败: {str(e)}")

@app.delete("/api/vms/{vm_id}/network-adapters/{adapter_index}")
async def remove_network_adapter(vm_id: str, adapter_index: int) -> Dict[str, Any]:
    """删除网络适配器"""
    try:
        success = config_parser.remove_network_adapter(vm_id, adapter_index)
        if not success:
            raise HTTPException(status_code=400, detail="删除网络适配器失败")

        return {"success": True, "message": "网络适配器删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除网络适配器失败: {str(e)}")



@app.get("/api/health")
async def health_check() -> Dict[str, str]:
    """健康检查"""
    return {"status": "healthy", "message": "VMware管理器运行正常"}

# 错误处理
@app.exception_handler(404)
async def not_found_handler(_: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={"error": "资源未找到", "detail": str(exc.detail)}
    )

@app.exception_handler(500)
async def internal_error_handler(_: Request, exc: HTTPException):
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误", "detail": str(exc.detail)}
    )

if __name__ == "__main__":
    import uvicorn
    print(f"启动VMware Workstation管理器...")
    print(f"访问地址: http://{Config.HOST}:{Config.PORT}")
    print(f"API文档: http://{Config.HOST}:{Config.PORT}/docs")
    
    uvicorn.run(
        "main:app",
        host=Config.HOST,
        port=Config.PORT,
        reload=Config.DEBUG
    )
                                                                                                                                    