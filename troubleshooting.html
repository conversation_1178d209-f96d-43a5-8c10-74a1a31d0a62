<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WCM - 故障排除</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; /* VMware 风格字体 */
            background-color: #f0f2f5; /* 浅灰色背景 */
            color: #333; /* 深色文本 */
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 移除赛博朋克背景效果 */
        body::before {
            content: none;
        }

        .header {
            background-color: #ffffff; /* 白色背景 */
            border-bottom: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 10;
        }

        .header h1 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #007bff; /* VMware 蓝色标题 */
            text-shadow: none;
            margin: 0;
            font-size: 1.8em;
            letter-spacing: 1px;
        }

        .header .nav-back a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1em;
            transition: color 0.2s;
        }

        .header .nav-back a:hover {
            color: #0056b3;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            gap: 20px;
            z-index: 1;
        }

        .control-panel {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            animation: none; /* 移除动画 */
        }

        .control-panel label {
            font-size: 1.1em;
            color: #333;
        }

        .control-panel select {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            border-radius: 4px;
            outline: none;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1em;
            box-shadow: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .control-panel select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .deploy-fault-button {
            padding: 12px 25px;
            background-color: #007bff; /* VMware 蓝色按钮 */
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s;
            letter-spacing: 0.5px;
        }

        .deploy-fault-button:hover {
            background-color: #0056b3;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .deploy-fault-button:active {
            transform: translateY(0);
        }

        .fault-info-container {
            background-color: #ffffff; /* 白色背景 */
            border: 1px solid #e0e0e0; /* 柔和边框 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 柔和阴影 */
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            animation: none; /* 移除动画 */
        }

        .fault-info-container h2 {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            color: #dc3545; /* 红色标题表示故障 */
            text-shadow: none;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .fault-info-container p {
            color: #555;
            margin-bottom: 10px;
        }

        .terminal-area {
            flex: 1;
            background-color: #2d2d2d; /* 深色终端背景 */
            border: 1px solid #444;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9em;
            color: #00ff00; /* 终端文本颜色 - 绿色 */
            overflow-y: auto;
            white-space: pre-wrap;
            height: 300px;
            position: relative;
            animation: none; /* 移除动画 */
        }

        .terminal-area::before {
            content: 'WCM >_ ';
            color: #007bff; /* 提示符颜色 */
        }

        .terminal-area::after {
            content: '_';
            animation: blink-caret 1s step-end infinite;
        }

        @keyframes blink-caret {
            from, to { opacity: 0; }
            50% { opacity: 1; }
        }

        /* 移除 SVG 装饰 */
        .svg-decoration {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>WCM</h1>
        <div class="nav-back">
            <a href="dashboard.html">< 返回仪表板</a>
        </div>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <label for="fault-scenario-select">选择故障场景:</label>
            <select id="fault-scenario-select">
                <option value="network-down">网络中断</option>
                <option value="service-failure">服务故障 (HTTP/DNS)</option>
                <option value="firewall-misconfig">防火墙配置错误</option>
                <option value="dns-poisoning">DNS投毒</option>
                <option value="router-loop">路由器环路</option>
                <!-- 更多故障场景 -->
            </select>
            <label for="fault-topology-select">选择目标拓扑:</label>
            <select id="fault-topology-select">
                <option value="complex-enterprise">复杂企业网络 (20+ VM)</option>
                <option value="multi-os-lab">多操作系统实验环境 (Win/Linux/Cisco)</option>
                <!-- 更多复杂拓扑 -->
            </select>
            <button class="deploy-fault-button">批量部署故障场景</button>
        </div>

        <div class="fault-info-container" id="fault-info-container">
            <h2>当前故障信息</h2>
            <p id="fault-status">状态: 无故障场景正在运行。</p>
            <p id="last-deployed-fault">上次部署故障: 无</p>
            <p id="fault-hint">提示: 选择一个故障场景和目标拓扑，然后点击“批量部署故障场景”按钮。</p>
        </div>

        <div class="terminal-area">
            <!-- 模拟终端输出将显示在这里 -->
            正在等待故障场景部署指令...
            <br>
            <br>
            [2025-07-12 00:05:00] 系统启动，准备就绪。
            <br>
            [2025-07-12 00:05:05] 检测到可用故障场景和拓扑。
            <br>
            [2025-07-12 00:05:10] 请选择故障场景和目标拓扑，然后点击“批量部署故障场景”按钮。
        </div>

        <!-- 移除 SVG 装饰 -->
        <!-- <svg class="svg-decoration svg-fault" viewBox="0 0 250 250" xmlns="http://www.w3.org/2000/svg">
            <circle cx="125" cy="125" r="100" stroke-dasharray="10 10" />
            <line x1="50" y1="50" x2="200" y2="200" stroke="#ff0000" stroke-width="3"/>
            <line x1="50" y1="200" x2="200" y2="50" stroke="#ff0000" stroke-width="3"/>
            <path d="M125 25 L145 55 L125 85 L105 55 Z" fill="#ff0000" />
            <path d="M125 225 L145 195 L125 165 L105 195 Z" fill="#ff0000" />
            <path d="M25 125 L55 145 L85 125 L55 105 Z" fill="#ff0000" />
            <path d="M225 125 L195 145 L165 125 L195 105 Z" fill="#ff0000" />
            <text x="125" y="135" text-anchor="middle" fill="#0a0a0a" font-size="24" font-weight="bold">!</text>
        </svg> -->
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const deployFaultButton = document.querySelector('.deploy-fault-button');
            const faultScenarioSelect = document.getElementById('fault-scenario-select');
            const faultTopologySelect = document.getElementById('fault-topology-select');
            const terminalArea = document.querySelector('.terminal-area');
            const faultStatus = document.getElementById('fault-status');
            const lastDeployedFault = document.getElementById('last-deployed-fault');
            const faultHint = document.getElementById('fault-hint');
            const faultInfoContainer = document.getElementById('fault-info-container');

            const faultScenarios = {
                'network-down': {
                    name: '网络中断',
                    description: '模拟核心网络设备故障，导致部分或全部网络服务中断。',
                    hint: '请检查路由器R1的接口配置和路由表。'
                },
                'service-failure': {
                    name: '服务故障 (HTTP/DNS)',
                    description: '模拟Web服务器或DNS服务器服务异常，导致应用无法访问或域名解析失败。',
                    hint: '检查Web服务器的HTTP服务状态或DNS服务器的解析记录。'
                },
                'firewall-misconfig': {
                    name: '防火墙配置错误',
                    description: '模拟防火墙规则配置不当，导致合法流量被阻断。',
                    hint: '审查防火墙ACL规则，确保允许必要的流量通过。'
                },
                'dns-poisoning': {
                    name: 'DNS投毒',
                    description: '模拟DNS缓存投毒攻击，导致用户访问到错误的IP地址。',
                    hint: '清理DNS缓存，并检查DNS服务器的安全性。'
                },
                'router-loop': {
                    name: '路由器环路',
                    description: '模拟路由协议配置错误导致的网络环路，造成广播风暴和网络性能下降。',
                    hint: '检查路由器的路由表和路由协议配置，查找并消除环路。'
                }
            };

            deployFaultButton.addEventListener('click', function() {
                const selectedScenarioId = faultScenarioSelect.value;
                const selectedTopologyId = faultTopologySelect.value;
                const selectedScenario = faultScenarios[selectedScenarioId];
                const topologyName = faultTopologySelect.options[faultTopologySelect.selectedIndex].text;

                if (!selectedScenario) {
                    terminalArea.innerHTML = `WCM >_ <span style="color: #ff3333;">错误: 无效的故障场景。</span><br>`;
                    terminalArea.scrollTop = terminalArea.scrollHeight;
                    return;
                }

                terminalArea.innerHTML = `WCM >_ 正在部署故障场景 "${selectedScenario.name}" 到拓扑 "${topologyName}"...<br><br>`;
                terminalArea.scrollTop = terminalArea.scrollHeight;

                faultStatus.textContent = `状态: 正在部署故障场景...`;
                lastDeployedFault.textContent = `上次部署故障: ${selectedScenario.name} (${topologyName})`;
                faultHint.textContent = `提示: ${selectedScenario.hint}`;
                faultInfoContainer.style.borderColor = '#dc3545'; // 红色边框
                faultInfoContainer.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05)'; // 柔和阴影

                const logs = [
                    `正在加载故障注入模块...`,
                    `正在分析目标拓扑结构...`,
                    `正在注入故障点: ${selectedScenario.description}`,
                    `故障场景 "${selectedScenario.name}" 已成功部署。`,
                    `请开始故障排除。`
                ];

                let logIndex = 0;
                const logInterval = setInterval(() => {
                    if (logIndex < logs.length) {
                        terminalArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${logs[logIndex]}<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                        logIndex++;
                    } else {
                        clearInterval(logInterval);
                        faultStatus.textContent = `状态: 故障场景 "${selectedScenario.name}" 正在运行。`;
                        terminalArea.innerHTML += `<br>WCM >_ 故障场景部署完成，等待排除。<br>`;
                        terminalArea.scrollTop = terminalArea.scrollHeight;
                    }
                }, 1500); // 每1.5秒输出一条日志
            });
        });
    </script>
</body>
</html>